[{"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000114.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第114帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [258.4, 312.0, 288.8, 337.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000178.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第178帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [276.0, 250.0, 307.2, 285.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000131.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第131帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [304.0, 254.0, 339.2, 283.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000113.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第113帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [330.4, 249.0, 365.6, 287.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000028.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第28帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [296.8, 241.0, 332.0, 278.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000077.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第77帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [296.8, 248.0, 334.4, 282.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000171.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第171帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [308.0, 247.0, 345.6, 284.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000165.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第165帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [237.6, 283.0, 264.8, 309.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000186.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第186帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [318.4, 319.0, 356.8, 346.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/65.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第65帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [135.5, 28.5, 170.5, 63.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000144.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第144帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [382.4, 258.0, 403.2, 270.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000061.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第61帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [335.2, 261.0, 356.8, 272.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000133.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第133帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [291.2, 230.0, 328.8, 254.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/15.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第15帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [178.5, 77.5, 213.5, 112.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000149.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第149帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [142.4, 252.0, 171.2, 282.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000072.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第72帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [338.4, 271.0, 361.6, 284.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000079.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第79帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [296.8, 249.0, 334.4, 283.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000071.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第71帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [298.4, 248.0, 334.4, 280.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000143.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第143帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [340.8, 250.0, 372.0, 284.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/75.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第75帧，前5帧平均目标数1.0，后3帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [131.5, 15.5, 166.5, 50.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000172.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第172帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [280.0, 297.0, 307.2, 322.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000142.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第142帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [376.0, 263.0, 396.0, 274.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000183.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第183帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [345.6, 316.0, 376.8, 343.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000069.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第69帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [303.2, 302.0, 336.8, 329.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000162.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第162帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [220.0, 277.0, 247.2, 304.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000096.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第96帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [396.8, 248.0, 432.8, 282.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000108.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第108帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [340.0, 277.0, 360.0, 289.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000001.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第1帧，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [289.6, 306.0, 319.2, 330.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000006.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第6帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [332.0, 263.0, 362.4, 291.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000173.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第173帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [293.6, 247.0, 332.0, 282.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000119.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第119帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [340.0, 277.0, 360.0, 287.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data03/4.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第4帧，前4帧平均目标数0.0，后5帧平均目标数0.0"}, {"from": "gpt", "value": "[]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000051.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第51帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [292.8, 242.0, 331.2, 278.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000107.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第107帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [330.4, 251.0, 363.2, 285.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000021.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第21帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [299.2, 231.0, 330.4, 260.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000155.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第155帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [177.6, 263.0, 205.6, 291.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/78.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第78帧，前5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [129.5, 34.5, 164.5, 69.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000182.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第182帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [340.0, 314.0, 371.2, 341.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000015.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第15帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [354.4, 229.0, 385.6, 260.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000111.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第111帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [243.2, 272.0, 273.6, 300.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/27.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第27帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [182.5, 55.5, 217.5, 90.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/12.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第12帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [194.5, 83.5, 229.5, 118.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000102.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第102帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [339.2, 278.0, 358.4, 289.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000036.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第36帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [286.4, 276.0, 308.8, 286.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/7.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第7帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [202.5, 93.5, 237.5, 128.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000098.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第98帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [396.8, 247.0, 432.0, 283.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000132.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第132帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [340.8, 277.0, 360.0, 288.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000098.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第98帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [169.6, 231.0, 200.8, 264.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000280.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第280帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [429.6, 126.0, 451.2, 137.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000190.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第190帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [468.0, 187.0, 489.6, 196.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000027.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第27帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [212.0, 253.0, 244.0, 275.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000083.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第83帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [296.8, 250.0, 333.6, 283.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000293.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第293帧，前5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [432.8, 128.0, 455.2, 140.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000001.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第1帧，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [0.0, 222.0, 21.6, 240.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000107.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第107帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [220.0, 248.0, 251.2, 276.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000040.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第40帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [288.0, 269.0, 312.8, 281.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/42.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第42帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [171.5, 28.5, 206.5, 63.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000080.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第80帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [386.4, 210.0, 412.0, 234.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000056.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第56帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [297.6, 247.0, 332.0, 278.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000030.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第30帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [312.8, 286.0, 339.2, 298.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000169.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第169帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [322.4, 247.0, 359.2, 285.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000129.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第129帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [339.2, 223.0, 367.2, 248.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/44.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第44帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [166.5, 30.5, 201.5, 65.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data03/14.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第14帧，前5帧平均目标数0.4，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [398.0, 114.0, 458.0, 174.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000058.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第58帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [331.2, 263.0, 352.8, 274.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/20.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第20帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [179.5, 68.5, 214.5, 103.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000101.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第101帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [185.6, 236.0, 216.0, 270.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000281.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第281帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [429.6, 126.0, 452.0, 137.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000089.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第89帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [340.0, 279.0, 360.0, 292.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000118.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第118帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [340.8, 277.0, 360.0, 287.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/32.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第32帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [172.5, 46.5, 207.5, 81.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000239.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第239帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [436.0, 129.0, 457.6, 141.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000197.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第197帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [475.2, 176.0, 497.6, 187.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000153.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第153帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [382.4, 246.0, 400.8, 257.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000070.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第70帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [310.4, 303.0, 344.0, 330.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000064.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第64帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [267.2, 296.0, 300.8, 323.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000187.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第187帧，前5帧平均目标数1.0，后2帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [380.8, 250.0, 412.8, 284.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000171.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第171帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [431.2, 215.0, 452.0, 226.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000103.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第103帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [338.4, 278.0, 358.4, 289.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000063.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第63帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [341.6, 260.0, 363.2, 272.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000164.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第164帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [232.0, 281.0, 259.2, 308.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000010.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第10帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [365.6, 224.0, 392.8, 251.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000111.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第111帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [331.2, 249.0, 365.6, 287.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000085.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第85帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [248.0, 217.0, 281.6, 243.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000007.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第7帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [461.6, 356.0, 484.0, 368.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000174.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第174帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [292.0, 301.0, 319.2, 326.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000291.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第291帧，前5帧平均目标数1.0，后2帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [432.0, 129.0, 456.0, 138.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data03/1.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第1帧，前1帧平均目标数0.0，后5帧平均目标数0.0"}, {"from": "gpt", "value": "[]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000138.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第138帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [294.4, 256.0, 325.6, 286.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000170.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第170帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [268.0, 293.0, 295.2, 318.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/60.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第60帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [149.5, 27.5, 184.5, 62.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000107.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第107帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [339.2, 277.0, 360.0, 289.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/14.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第14帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [179.5, 79.5, 214.5, 114.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000117.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第117帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [275.2, 316.0, 305.6, 341.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000036.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第36帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [250.4, 263.0, 288.0, 288.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000063.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第63帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [260.0, 295.0, 293.6, 322.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000268.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第268帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [431.2, 128.0, 452.8, 137.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/71.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第71帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [132.5, 18.5, 167.5, 53.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000241.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第241帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [435.2, 129.0, 456.0, 139.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000173.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第173帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [285.6, 299.0, 312.8, 324.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000247.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第247帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [435.2, 127.0, 456.8, 138.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000143.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第143帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [379.2, 260.0, 398.4, 272.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data03/13.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第13帧，前5帧平均目标数0.2，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [408.0, 118.0, 468.0, 178.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000217.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第217帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [434.4, 141.0, 456.0, 152.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000009.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第9帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [162.4, 219.0, 196.8, 245.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000289.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第289帧，前5帧平均目标数1.0，后4帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [432.0, 129.0, 455.2, 138.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000175.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第175帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [432.0, 208.0, 452.8, 219.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000068.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第68帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [342.4, 261.0, 363.2, 273.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000183.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第183帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [350.4, 251.0, 383.2, 282.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000055.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第55帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [296.8, 246.0, 332.0, 278.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000093.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第93帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [177.6, 231.0, 212.8, 253.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000172.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第172帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [300.8, 247.0, 338.4, 283.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000113.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第113帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [252.8, 311.0, 284.0, 336.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000145.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第145帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [385.6, 257.0, 404.8, 269.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000175.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第175帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [297.6, 302.0, 325.6, 327.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000171.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第171帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [273.6, 295.0, 300.8, 320.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000095.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第95帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [396.8, 249.0, 433.6, 281.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000091.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第91帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [338.4, 280.0, 360.8, 291.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000108.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第108帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [331.2, 250.0, 364.8, 286.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000084.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第84帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [278.4, 215.0, 312.0, 241.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000090.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第90帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [344.8, 251.0, 379.2, 283.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000119.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第119帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [286.4, 318.0, 316.8, 343.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/38.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第38帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [171.5, 34.5, 206.5, 69.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000083.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第83帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [308.8, 213.0, 342.4, 238.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000169.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第169帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [261.6, 291.0, 288.8, 317.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000156.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第156帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [264.0, 249.0, 300.8, 285.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000154.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第154帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [382.4, 244.0, 400.8, 256.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/1.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第1帧，前1帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [197.5, 104.5, 232.5, 139.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000215.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第215帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [440.0, 144.0, 462.4, 154.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000068.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第68帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [296.0, 301.0, 329.6, 328.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000062.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第62帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [338.4, 260.0, 361.6, 272.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000178.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第178帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [427.2, 205.0, 449.6, 215.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000093.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第93帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [338.4, 278.0, 359.2, 292.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000287.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第287帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [432.0, 126.0, 454.4, 138.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000022.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第22帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [305.6, 244.0, 335.2, 270.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000100.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第100帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [180.0, 234.0, 210.4, 269.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000245.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第245帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [434.4, 128.0, 457.6, 139.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000210.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第210帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [436.0, 154.0, 462.4, 166.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000070.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第70帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [298.4, 248.0, 334.4, 280.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000032.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第32帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [301.6, 283.0, 325.6, 296.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000072.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第72帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [297.6, 248.0, 334.4, 281.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000001.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第1帧，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [506.4, 365.0, 512.8, 373.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000274.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第274帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [431.2, 126.0, 452.8, 138.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/67.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第67帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [135.5, 24.5, 170.5, 59.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000067.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第67帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [288.8, 300.0, 322.4, 327.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000232.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第232帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [437.6, 130.0, 460.0, 141.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000116.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第116帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [340.0, 277.0, 359.2, 288.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000075.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第75帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [352.0, 301.0, 379.2, 328.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000023.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第23帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [350.4, 304.0, 371.2, 314.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000023.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第23帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [296.0, 244.0, 329.6, 275.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/0.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第0帧，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [197.5, 106.5, 232.5, 141.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data22/merged_dataset_data_transform_1_20190925_111757_1_8_000109.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第109帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [332.8, 250.0, 367.2, 288.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data05/51.bmp<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第51帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [160.5, 22.5, 195.5, 57.5], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000008.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第8帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [450.4, 353.0, 469.6, 364.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000172.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第172帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [433.6, 213.0, 454.4, 225.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data21/merged_dataset_data_transform_1_20190925_101846_1_3_000044.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第44帧，前5帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [227.2, 272.0, 257.6, 297.0], \"label\": \"drone\"}]"}]}, {"conversations": [{"from": "human", "value": "<|vision_start|>../Processed_data/data01-15/big/data20/merged_dataset_data_transform_1_01_4485_1167-2666_000004.jpg<|vision_end|>请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一：drone, car, ship, bus, pedestrian, cyclist。\n格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n时序上下文：当前帧第4帧，前3帧平均目标数1.0，后5帧平均目标数1.0"}, {"from": "gpt", "value": "[{\"bbox\": [484.0, 361.0, 505.6, 371.0], \"label\": \"drone\"}]"}]}]