#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试评估结果为0的问题
"""

import os
import sys
import json
import logging
from pathlib import Path
from collections import defaultdict
from dataclasses import dataclass
from typing import List, Dict, Tuple
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class DetectionResult:
    """检测结果数据类"""
    bbox: List[float]  # [x1, y1, x2, y2]
    label: str
    confidence: float
    frame_id: str
    sequence_id: str
    temporal_score: float = 0.5

@dataclass
class GroundTruth:
    """真实标注数据类"""
    bbox: List[float]  # [x1, y1, x2, y2]
    label: str
    frame_id: str
    sequence_id: str

def natural_sort_key(filename: str):
    """自然排序键函数"""
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return [int(numbers[-1])]
    else:
        return [filename.lower()]

def extract_frame_number(filename: str) -> int:
    """从文件名中提取帧序号"""
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return int(numbers[-1])
    else:
        return 0

def load_class_map(class_json_path: str):
    with open(class_json_path, 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    # 反向映射：所有英文名映射成自己
    class_map_rev = {v: v for v in class_map.values()}
    # 可扩展：如有中英文对照，可在此补充
    zh2en = {"无人机": "drone", "汽车": "car", "轮船": "ship", "公交车": "bus", "行人": "pedestrian", "骑行者": "cyclist", "弱小目标": "unknown"}
    class_map_rev.update(zh2en)
    return class_map, class_map_rev

def load_detection_results(json_path: str) -> List[DetectionResult]:
    """加载检测结果"""
    results = []
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    for item in data:
        result = DetectionResult(
            bbox=item['bbox'],
            label=item['label'],
            confidence=item['confidence'],
            frame_id=item['frame_id'],
            sequence_id=item['sequence_id'],
            temporal_score=item.get('temporal_score', 0.5)
        )
        results.append(result)
    
    return results

def load_ground_truth_debug(labels_root: str, class_json_path: str, img_root: str, sample_ratio: float = 0.2, target_sequences: list = None):
    """
    调试版本的标注加载函数
    """
    class_map, _ = load_class_map(class_json_path)
    ground_truth = defaultdict(list)
    
    logger.info(f"开始加载标注数据，标注路径: {labels_root}")
    logger.info(f"图像路径: {img_root}")
    logger.info(f"类别映射: {class_map}")
    
    for seq in sorted(os.listdir(labels_root)):
        if target_sequences and seq not in target_sequences:
            continue
            
        seq_label_path = os.path.join(labels_root, seq)
        seq_img_path = os.path.join(img_root, seq)
        
        if not os.path.isdir(seq_label_path):
            continue
            
        logger.info(f"处理序列: {seq}")
        logger.info(f"  标注路径: {seq_label_path}")
        logger.info(f"  图像路径: {seq_img_path}")
        
        # 获取所有标注文件并使用自然排序
        label_files = []
        for label_file in os.listdir(seq_label_path):
            if label_file.endswith('.txt'):
                label_files.append(label_file)
        
        # 自然排序
        label_files.sort(key=natural_sort_key)
        
        # 只处理前sample_ratio比例的文件
        num_files = len(label_files)
        num_to_process = max(1, int(num_files * sample_ratio))
        selected_files = label_files[:num_to_process]
        
        logger.info(f"  序列 {seq}: 共 {num_files} 个文件，处理前 {num_to_process} 个 ({sample_ratio*100:.2f}%)")
        
        for idx, label_file in enumerate(selected_files):
            label_path = os.path.join(seq_label_path, label_file)
            
            # 提取帧ID
            frame_id = str(extract_frame_number(label_file))
            
            # 获取对应的图像文件来确定图像尺寸
            img_file_base = label_file.replace('.txt', '.jpg')
            img_path = os.path.join(seq_img_path, img_file_base)
            
            if not os.path.exists(img_path):
                logger.warning(f"  图像文件不存在: {img_path}")
                continue
            
            # 获取图像尺寸
            try:
                from PIL import Image
                with Image.open(img_path) as img:
                    w, h = img.size
            except Exception as e:
                logger.warning(f"  无法获取图像尺寸 {img_path}: {e}")
                w, h = 640, 512  # 默认尺寸
            
            # 解析标注文件
            has_annotations = False
            with open(label_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    line = line.strip()
                    if not line:
                        continue
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                    cls_id, cx, cy, bw, bh = parts[:5]
                    label = class_map.get(str(cls_id), 'unknown')
                    cx, cy, bw, bh = map(float, [cx, cy, bw, bh])
                    
                    # 转换为绝对坐标
                    x1 = (cx - bw/2) * w
                    y1 = (cy - bh/2) * h
                    x2 = (cx + bw/2) * w
                    y2 = (cy + bh/2) * h
                    
                    gt = GroundTruth(
                        bbox=[x1, y1, x2, y2],
                        label=label,
                        frame_id=frame_id,
                        sequence_id=seq
                    )
                    ground_truth[seq].append(gt)
                    has_annotations = True
                    
                    # 打印前几个标注的详细信息
                    if idx < 3 and line_num < 2:
                        logger.info(f"    标注详情 - 帧{frame_id}: YOLO({cx:.3f},{cy:.3f},{bw:.3f},{bh:.3f}) -> 绝对坐标({x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}), 类别: {label}")
            
            # 确保即使是空标注文件也在ground_truth中有对应的序列条目
            if not has_annotations:
                if seq not in ground_truth:
                    ground_truth[seq] = []
                logger.info(f"    帧{frame_id}: 空标注")
    
    return ground_truth

def calculate_iou(bbox1: List[float], bbox2: List[float]) -> float:
    """计算两个边界框的IoU"""
    x1_1, y1_1, x2_1, y2_1 = bbox1
    x1_2, y1_2, x2_2, y2_2 = bbox2
    
    # 计算交集
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0
    
    intersection = (x2_i - x1_i) * (y2_i - y1_i)
    
    # 计算并集
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0

def debug_frame_matching(predictions: List[DetectionResult], ground_truths: List[GroundTruth], frame_id: str, seq_id: str, iou_threshold: float = 0.3):
    """调试单帧的匹配情况"""
    logger.info(f"调试帧匹配 - 序列{seq_id}, 帧{frame_id}")
    logger.info(f"  预测数量: {len(predictions)}")
    logger.info(f"  标注数量: {len(ground_truths)}")
    
    if predictions:
        logger.info(f"  预测详情:")
        for i, pred in enumerate(predictions):
            logger.info(f"    预测{i}: bbox={pred.bbox}, label={pred.label}, conf={pred.confidence}")
    
    if ground_truths:
        logger.info(f"  标注详情:")
        for i, gt in enumerate(ground_truths):
            logger.info(f"    标注{i}: bbox={gt.bbox}, label={gt.label}")
    
    # 计算IoU矩阵
    if predictions and ground_truths:
        logger.info(f"  IoU矩阵:")
        for i, pred in enumerate(predictions):
            for j, gt in enumerate(ground_truths):
                iou = calculate_iou(pred.bbox, gt.bbox)
                label_match = pred.label == gt.label
                logger.info(f"    预测{i} vs 标注{j}: IoU={iou:.3f}, 类别匹配={label_match} ({pred.label} vs {gt.label})")
                if iou >= iou_threshold and label_match:
                    logger.info(f"      ✅ 匹配成功!")
                else:
                    logger.info(f"      ❌ 匹配失败 (IoU阈值={iou_threshold})")

def main():
    """主函数"""
    
    # 路径配置
    detection_results_path = "Output/processed_detection_results.json"
    annotation_path = "../tiaozhanbei_datasets/labels"
    class_json = "../tiaozhanbei_datasets/class.json"
    data_path = "../Processed data/data01-15/big"
    
    # 测试参数
    sample_ratio = 0.2
    iou_threshold = 0.3
    target_sequences = ["data03", "data05", "data20", "data21"]
    
    logger.info("=== 调试评估结果为0的问题 ===")
    
    # 1. 加载检测结果
    logger.info("1. 加载检测结果")
    detection_results = load_detection_results(detection_results_path)
    logger.info(f"总检测结果数量: {len(detection_results)}")
    
    # 按序列分组
    predictions_by_seq = defaultdict(list)
    for result in detection_results:
        predictions_by_seq[result.sequence_id].append(result)
    
    logger.info(f"检测到的序列: {list(predictions_by_seq.keys())}")
    for seq_id, results in predictions_by_seq.items():
        logger.info(f"  序列 {seq_id}: {len(results)} 个检测结果")
    
    # 2. 加载标注数据
    logger.info("\n2. 加载标注数据")
    ground_truth = load_ground_truth_debug(annotation_path, class_json, data_path, sample_ratio, target_sequences)
    
    logger.info(f"加载的标注序列: {list(ground_truth.keys())}")
    for seq_id, gts in ground_truth.items():
        logger.info(f"  序列 {seq_id}: {len(gts)} 个标注")
    
    # 3. 检查序列匹配
    logger.info("\n3. 检查序列匹配")
    common_sequences = set(predictions_by_seq.keys()) & set(ground_truth.keys())
    logger.info(f"共同序列: {list(common_sequences)}")
    
    if not common_sequences:
        logger.error("❌ 没有共同的序列！检测结果和标注数据不匹配")
        return
    
    # 4. 详细分析每个序列的前几帧
    logger.info("\n4. 详细分析每个序列的前几帧")
    for seq_id in list(common_sequences)[:2]:  # 只分析前2个序列
        logger.info(f"\n分析序列: {seq_id}")
        
        # 按帧分组
        pred_by_frame = defaultdict(list)
        gt_by_frame = defaultdict(list)
        
        for pred in predictions_by_seq[seq_id]:
            pred_by_frame[pred.frame_id].append(pred)
        
        for gt in ground_truth[seq_id]:
            gt_by_frame[gt.frame_id].append(gt)
        
        logger.info(f"  预测帧: {sorted(pred_by_frame.keys())}")
        logger.info(f"  标注帧: {sorted(gt_by_frame.keys())}")
        
        # 分析前几帧
        all_frames = sorted(set(pred_by_frame.keys()) | set(gt_by_frame.keys()))
        for frame_id in all_frames[:3]:  # 只分析前3帧
            frame_preds = pred_by_frame[frame_id]
            frame_gts = gt_by_frame[frame_id]
            debug_frame_matching(frame_preds, frame_gts, frame_id, seq_id, iou_threshold)

if __name__ == "__main__":
    main()
