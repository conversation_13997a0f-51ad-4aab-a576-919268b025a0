# BigGoal弱小目标检测项目

基于Qwen2.5-VL的红外弱小目标检测系统，专门针对5个序列（data03, data05, data20, data21, data22）进行优化。

## 项目结构

```
BigGoal_finetune/
├── data_processor.py      # 数据处理脚本
├── train_lora.py         # LoRA微调脚本
├── detection_evaluation.py # 检测评估脚本
├── README.md             # 项目说明文档
└── output/               # 输出目录
    ├── dataset/          # 处理后的训练数据
    ├── model/            # 微调后的模型
    └── results/          # 检测和评估结果
```

## 环境要求

- Python 3.10+
- PyTorch 2.0+
- transformers >= 4.37.0
- CUDA 11.8+
- 其他依赖：PIL, numpy, tqdm, swanlab

## 安装依赖

```bash
pip install torch torchvision transformers
pip install pillow numpy tqdm swanlab
pip install peft datasets
```

## 使用流程

### 1. 数据处理

将YOLO格式的标注数据转换为Qwen2.5-VL微调格式：

```bash
python data_processor.py \
    --labels_root /path/to/labels \
    --images_root /path/to/images \
    --class_json /path/to/class.json \
    --output_dir output/dataset \
    --sample_ratio 0.2
```

**参数说明：**
- `--labels_root`: YOLO标注文件根目录
- `--images_root`: 图像文件根目录  
- `--class_json`: 类别映射文件
- `--output_dir`: 输出目录
- `--sample_ratio`: 数据采样比例（默认0.2，即20%）

**输出文件：**
- `biggoal_train_data.json`: 训练数据
- `biggoal_stats.json`: 数据统计信息

### 2. LoRA微调

使用处理后的数据进行LoRA微调：

```bash
CUDA_VISIBLE_DEVICES=0 python train_lora.py \
    --model_path /path/to/Qwen2.5-VL-7B-Instruct \
    --train_data output/dataset/biggoal_train_data.json \
    --output_dir output/model/biggoal_lora \
    --data_ratio 0.2 \
    --num_epochs 3 \
    --lora_rank 16 \
    --learning_rate 1e-5 \
    --swanlab_project "BigGoal_Finetune" \
    --experiment_name "biggoal_lora_v1"
```

**参数说明：**
- `--model_path`: 预训练模型路径
- `--train_data`: 训练数据文件
- `--output_dir`: 模型输出目录
- `--data_ratio`: 数据使用比例
- `--num_epochs`: 训练轮数
- `--lora_rank`: LoRA rank参数
- `--learning_rate`: 学习率
- `--swanlab_project`: SwanLab项目名
- `--experiment_name`: 实验名称

### 3. 检测评估

使用微调后的模型进行检测和评估：

```bash
CUDA_VISIBLE_DEVICES=0 python detection_evaluation.py \
    --model_path output/model/biggoal_lora \
    --data_path /path/to/images \
    --annotation_path /path/to/labels \
    --class_json /path/to/class.json \
    --output_dir output/results \
    --sample_ratio 0.2
```

**参数说明：**
- `--model_path`: 微调后的模型路径
- `--data_path`: 测试图像路径
- `--annotation_path`: 标注数据路径
- `--class_json`: 类别映射文件
- `--output_dir`: 结果输出目录
- `--sample_ratio`: 测试数据采样比例

**输出文件：**
- `biggoal_detection_results.json`: 检测结果
- `biggoal_evaluation_results.json`: 评估指标

## 目标序列

本项目专门针对以下5个序列进行优化：

1. **data03**: 小尺寸序列（256x256）
2. **data05**: 小尺寸序列（256x256）
3. **data20**: 标准尺寸序列（640x512）
4. **data21**: 标准尺寸序列（640x512）
5. **data22**: 标准尺寸序列（640x512）

## 特性

### 数据处理特性
- 支持自然排序，正确处理帧序
- 时序上下文信息提取
- 多种图像尺寸自适应
- YOLO到绝对坐标转换
- 数据质量验证和过滤

### 微调特性
- LoRA高效微调，减少显存占用
- 自定义数据收集器，处理视觉-语言模型特殊需求
- 梯度累积和混合精度训练
- SwanLab实验跟踪
- 数据验证和错误处理

### 检测特性
- 时间窗口检测，利用时序信息
- 多帧联合推理
- 置信度和时序得分评估
- 自动质量控制

### 评估特性
- IoU匹配算法
- 精确率、召回率、F1分数计算
- 时序一致性评估
- 详细的序列级统计

## 评估指标

- **精确率 (Precision)**: TP / (TP + FP)
- **召回率 (Recall)**: TP / (TP + FN)  
- **F1分数**: 2 * Precision * Recall / (Precision + Recall)
- **时序一致性**: 相邻帧检测结果的一致性
- **IoU阈值**: 默认0.3

## 类别映射

支持的目标类别：
- drone: 无人机
- car: 汽车
- ship: 轮船
- bus: 公交车
- pedestrian: 行人
- cyclist: 骑行者

## 注意事项

1. **GPU内存**: 建议使用16GB+显存的GPU
2. **数据路径**: 确保图像和标注文件路径正确对应
3. **文件格式**: 支持jpg、png、bmp等图像格式
4. **采样比例**: 可根据计算资源调整采样比例
5. **模型路径**: 确保Qwen2.5-VL模型路径正确

## 性能优化

- 使用梯度检查点减少显存占用
- 混合精度训练加速
- 数据预验证避免训练中断
- 批处理优化推理速度

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少batch_size
   - 启用梯度检查点
   - 使用更小的LoRA rank

2. **图像加载失败**
   - 检查图像路径和格式
   - 验证图像文件完整性

3. **JSON解析错误**
   - 检查模型输出格式
   - 调整生成参数

4. **评估结果为0**
   - 检查序列名称匹配
   - 验证坐标转换正确性
   - 确认类别映射一致

## 更新日志

- v1.0: 初始版本，支持5个序列的完整流程
- 优化了数据处理和微调流程
- 改进了检测评估算法
- 增加了详细的错误处理和日志

## 联系方式

如有问题请提交Issue或联系项目维护者。
