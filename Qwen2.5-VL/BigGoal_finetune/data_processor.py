#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BigGoal弱小目标检测数据处理器
将5个指定序列的YOLO格式数据转换为Qwen2.5-VL微调格式
支持时间窗口信息和高频弱小目标检测优化
"""

import os
import json
import numpy as np
from PIL import Image
from pathlib import Path
from typing import List, Dict, Tuple
import logging
from tqdm import tqdm
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BigGoalDataProcessor:
    """BigGoal弱小目标检测数据处理器"""
    
    def __init__(self, class_json_path: str = "class.json"):
        """
        初始化数据处理器
        
        Args:
            class_json_path: 类别映射文件路径
        """
        self.class_json_path = class_json_path
        self.class_map = self.load_class_map()
        
        # 5个指定序列 - BigGoal项目最终确定的序列
        self.target_sequences = ["data03", "data05", "data20", "data21", "data22"]
        
        logger.info(f"初始化BigGoal数据处理器，目标序列: {self.target_sequences}")
    
    def load_class_map(self) -> Dict[str, str]:
        """加载类别映射"""
        try:
            with open(self.class_json_path, 'r', encoding='utf-8') as f:
                class_map = json.load(f)
            logger.info(f"加载类别映射: {class_map}")
            return class_map
        except Exception as e:
            logger.warning(f"无法加载类别映射文件 {self.class_json_path}: {e}")
            # 使用默认映射
            default_map = {
                "0": "drone",
                "1": "car", 
                "2": "ship",
                "3": "bus",
                "4": "pedestrian",
                "5": "cyclist"
            }
            logger.info(f"使用默认类别映射: {default_map}")
            return default_map
    
    def natural_sort_key(self, filename: str) -> List:
        """自然排序键函数，正确处理数字排序"""
        import re
        numbers = re.findall(r'\d+', filename)
        if numbers:
            return [int(numbers[-1])]
        else:
            return [filename.lower()]
    
    def extract_frame_number(self, filename: str) -> int:
        """从文件名中提取帧序号"""
        import re
        numbers = re.findall(r'\d+', filename)
        if numbers:
            return int(numbers[-1])
        else:
            return 0
    
    def get_image_size(self, image_path: str) -> Tuple[int, int]:
        """获取图像尺寸"""
        try:
            with Image.open(image_path) as img:
                return img.size
        except Exception as e:
            logger.warning(f"无法获取图像尺寸 {image_path}: {e}")
            # 根据序列返回默认尺寸
            if any(seq in image_path for seq in ["data03", "data05"]):
                return (256, 256)
            else:
                return (640, 512)
    
    def convert_yolo_to_absolute(self, yolo_bbox: List[float], img_width: int, img_height: int) -> List[float]:
        """将YOLO格式坐标转换为绝对坐标"""
        cx, cy, w, h = yolo_bbox
        x1 = (cx - w/2) * img_width
        y1 = (cy - h/2) * img_height
        x2 = (cx + w/2) * img_width
        y2 = (cy + h/2) * img_height
        return [x1, y1, x2, y2]
    
    def calculate_temporal_context(self, sequence_data: List[Dict], current_idx: int, window_size: int = 5) -> str:
        """计算时序上下文信息"""
        total_frames = len(sequence_data)
        current_frame = current_idx + 1
        
        # 计算前后窗口的平均目标数
        start_idx = max(0, current_idx - window_size)
        end_idx = min(total_frames, current_idx + window_size + 1)
        
        before_frames = sequence_data[start_idx:current_idx]
        after_frames = sequence_data[current_idx + 1:end_idx]
        
        before_avg = np.mean([len(frame['objects']) for frame in before_frames]) if before_frames else 0
        after_avg = np.mean([len(frame['objects']) for frame in after_frames]) if after_frames else 0
        
        return f"当前帧第{current_frame}帧，前{len(before_frames)}帧平均目标数{before_avg:.1f}，后{len(after_frames)}帧平均目标数{after_avg:.1f}"
    
    def process_sequence(self, labels_dir: str, images_dir: str, sequence_id: str, 
                        sample_ratio: float = 0.2) -> List[Dict]:
        """处理单个序列"""
        sequence_label_dir = os.path.join(labels_dir, sequence_id)
        sequence_image_dir = os.path.join(images_dir, sequence_id)
        
        if not os.path.exists(sequence_label_dir) or not os.path.exists(sequence_image_dir):
            logger.warning(f"序列 {sequence_id} 的标注或图像目录不存在")
            return []
        
        # 获取所有标注文件
        label_files = [f for f in os.listdir(sequence_label_dir) if f.endswith('.txt')]
        label_files.sort(key=self.natural_sort_key)
        
        # 采样
        total_files = len(label_files)
        sample_count = max(1, int(total_files * sample_ratio))
        selected_files = label_files[:sample_count]
        
        logger.info(f"序列 {sequence_id}: 总文件数 {total_files}, 采样 {sample_count} 个")
        
        # 处理每个文件
        sequence_data = []
        for label_file in tqdm(selected_files, desc=f"处理 {sequence_id}"):
            frame_data = self.process_frame(
                sequence_label_dir, sequence_image_dir, label_file, sequence_id
            )
            if frame_data:
                sequence_data.append(frame_data)
        
        # 添加时序上下文
        for i, frame_data in enumerate(sequence_data):
            temporal_context = self.calculate_temporal_context(sequence_data, i)
            frame_data['temporal_context'] = temporal_context
        
        return sequence_data
    
    def process_frame(self, label_dir: str, image_dir: str, label_file: str, sequence_id: str) -> Dict:
        """处理单帧数据"""
        label_path = os.path.join(label_dir, label_file)
        image_file = label_file.replace('.txt', '.jpg')
        image_path = os.path.join(image_dir, image_file)
        
        if not os.path.exists(image_path):
            logger.warning(f"图像文件不存在: {image_path}")
            return None
        
        # 获取图像尺寸
        img_width, img_height = self.get_image_size(image_path)
        frame_id = self.extract_frame_number(label_file)
        
        # 解析标注
        objects = []
        try:
            with open(label_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                    
                    class_id = parts[0]
                    yolo_bbox = [float(x) for x in parts[1:5]]
                    
                    # 转换坐标
                    abs_bbox = self.convert_yolo_to_absolute(yolo_bbox, img_width, img_height)
                    
                    # 获取类别名称
                    class_name = self.class_map.get(class_id, 'unknown')
                    
                    objects.append({
                        'bbox': abs_bbox,
                        'label': class_name
                    })
        
        except Exception as e:
            logger.warning(f"解析标注文件失败 {label_path}: {e}")
        
        return {
            'image_path': image_path,
            'frame_id': frame_id,
            'sequence_id': sequence_id,
            'objects': objects,
            'image_size': (img_width, img_height)
        }
    
    def create_training_sample(self, frame_data: Dict) -> Dict:
        """创建训练样本"""
        # 构建检测提示词
        prompt = (
            "请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n"
            "注意：这是时序视频序列中的一帧，目标在相邻帧中通常具有连续性和一致性。\n"
            "请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一："
            "drone, car, ship, bus, pedestrian, cyclist。\n"
            "格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n"
            "提示：弱小目标在时序中通常保持相对稳定的位置和运动轨迹。\n"
            f"时序上下文：{frame_data['temporal_context']}"
        )
        
        # 构建回答
        if frame_data['objects']:
            answer_objects = []
            for obj in frame_data['objects']:
                answer_objects.append({
                    "bbox": [round(x, 1) for x in obj['bbox']],
                    "label": obj['label']
                })
            answer = json.dumps(answer_objects, ensure_ascii=False)
        else:
            answer = "[]"
        
        # 构建对话格式
        conversation = {
            "conversations": [
                {
                    "from": "human",
                    "value": f"<|vision_start|>{frame_data['image_path']}<|vision_end|>{prompt}"
                },
                {
                    "from": "gpt", 
                    "value": answer
                }
            ]
        }
        
        return conversation
    
    def process_all_sequences(self, labels_root: str, images_root: str, 
                            output_dir: str = "output/dataset", 
                            sample_ratio: float = 0.2) -> Dict[str, str]:
        """处理所有序列并生成训练数据"""
        os.makedirs(output_dir, exist_ok=True)
        
        all_samples = []
        stats = {
            'total_sequences': len(self.target_sequences),
            'total_frames': 0,
            'total_objects': 0,
            'sequence_stats': {}
        }
        
        for sequence_id in self.target_sequences:
            logger.info(f"开始处理序列: {sequence_id}")
            sequence_data = self.process_sequence(
                labels_root, images_root, sequence_id, sample_ratio
            )
            
            sequence_samples = []
            sequence_objects = 0
            
            for frame_data in sequence_data:
                sample = self.create_training_sample(frame_data)
                sequence_samples.append(sample)
                all_samples.append(sample)
                sequence_objects += len(frame_data['objects'])
            
            stats['sequence_stats'][sequence_id] = {
                'frames': len(sequence_data),
                'objects': sequence_objects
            }
            stats['total_frames'] += len(sequence_data)
            stats['total_objects'] += sequence_objects
            
            logger.info(f"序列 {sequence_id} 完成: {len(sequence_data)} 帧, {sequence_objects} 个目标")
        
        # 保存数据
        train_file = os.path.join(output_dir, "biggoal_train_data.json")
        stats_file = os.path.join(output_dir, "biggoal_stats.json")
        
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(all_samples, f, ensure_ascii=False, indent=2)
        
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据处理完成:")
        logger.info(f"  总样本数: {len(all_samples)}")
        logger.info(f"  总帧数: {stats['total_frames']}")
        logger.info(f"  总目标数: {stats['total_objects']}")
        logger.info(f"  训练文件: {train_file}")
        logger.info(f"  统计文件: {stats_file}")
        
        return {
            'train_file': train_file,
            'stats_file': stats_file
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='BigGoal弱小目标检测数据处理')
    parser.add_argument('--labels_root', type=str, required=True, help='标注文件根目录')
    parser.add_argument('--images_root', type=str, required=True, help='图像文件根目录')
    parser.add_argument('--class_json', type=str, default='class.json', help='类别映射文件')
    parser.add_argument('--output_dir', type=str, default='output/dataset', help='输出目录')
    parser.add_argument('--sample_ratio', type=float, default=0.2, help='采样比例')
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = BigGoalDataProcessor(args.class_json)
    
    # 处理数据
    result = processor.process_all_sequences(
        args.labels_root,
        args.images_root, 
        args.output_dir,
        args.sample_ratio
    )
    
    logger.info("数据处理完成！")

if __name__ == "__main__":
    main()
