#!/bin/bash
# BigGoal弱小目标检测项目运行示例

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0

# 项目路径配置
PROJECT_ROOT="/home/<USER>/Qwen/Qwen2.5-VL/BigGoal_finetune"
MODEL_PATH="/home/<USER>/Qwen/Qwen2.5-VL-7B-Instruct"
DATA_ROOT="/home/<USER>/Qwen/Processed_data/data01-15/big"
LABELS_ROOT="/home/<USER>/Qwen/tiaozhanbei_datasets/labels"
CLASS_JSON="/home/<USER>/Qwen/tiaozhanbei_datasets/class.json"

# 激活虚拟环境
source activate Qwen

echo "=== BigGoal弱小目标检测项目 ==="
echo "目标序列: data03, data05, data20, data21, data22"
echo ""

# 1. 数据处理
echo "步骤1: 数据处理..."
python data_processor.py \
    --labels_root "$LABELS_ROOT" \
    --images_root "$DATA_ROOT" \
    --class_json "$CLASS_JSON" \
    --output_dir "output/dataset" \
    --sample_ratio 0.2

if [ $? -ne 0 ]; then
    echo "数据处理失败！"
    exit 1
fi

echo "数据处理完成！"
echo ""

# 2. LoRA微调
echo "步骤2: LoRA微调..."
python train_lora.py \
    --model_path "$MODEL_PATH" \
    --train_data "output/dataset/biggoal_train_data.json" \
    --output_dir "output/model/biggoal_lora" \
    --data_ratio 0.2 \
    --num_epochs 3 \
    --lora_rank 16 \
    --learning_rate 1e-5 \
    --swanlab_project "BigGoal_Finetune" \
    --experiment_name "biggoal_lora_$(date +%Y%m%d_%H%M%S)"

if [ $? -ne 0 ]; then
    echo "微调失败！"
    exit 1
fi

echo "微调完成！"
echo ""

# 3. 检测评估
echo "步骤3: 检测评估..."
python detection_evaluation.py \
    --model_path "output/model/biggoal_lora" \
    --data_path "$DATA_ROOT" \
    --annotation_path "$LABELS_ROOT" \
    --class_json "$CLASS_JSON" \
    --output_dir "output/results" \
    --sample_ratio 0.2

if [ $? -ne 0 ]; then
    echo "检测评估失败！"
    exit 1
fi

echo "检测评估完成！"
echo ""

# 显示结果
echo "=== 运行完成 ==="
echo "结果文件："
echo "  训练数据: output/dataset/biggoal_train_data.json"
echo "  微调模型: output/model/biggoal_lora/"
echo "  检测结果: output/results/biggoal_detection_results.json"
echo "  评估结果: output/results/biggoal_evaluation_results.json"
echo ""

# 显示评估指标
if [ -f "output/results/biggoal_evaluation_results.json" ]; then
    echo "评估指标："
    python -c "
import json
with open('output/results/biggoal_evaluation_results.json', 'r') as f:
    results = json.load(f)
print(f'  精确率: {results[\"precision\"]:.4f}')
print(f'  召回率: {results[\"recall\"]:.4f}')
print(f'  F1分数: {results[\"f1_score\"]:.4f}')
print(f'  时序一致性: {results[\"temporal_consistency\"]:.4f}')
"
fi

echo "项目运行完成！"
