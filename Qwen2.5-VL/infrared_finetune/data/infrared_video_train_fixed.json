[{"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"1\": [], \"6\": [], \"11\": [], \"16\": [], \"21\": []}"}], "video": "data02_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"2\": [], \"7\": [], \"12\": [], \"17\": [], \"22\": []}"}], "video": "data02_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"3\": [], \"8\": [], \"13\": [], \"18\": [], \"23\": []}"}], "video": "data02_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"4\": [], \"9\": [], \"14\": [], \"19\": [], \"24\": []}"}], "video": "data02_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"5\": [], \"10\": [], \"15\": [], \"20\": [], \"25\": []}"}], "video": "data02_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"26\": [], \"31\": [], \"36\": [], \"41\": [], \"46\": []}"}], "video": "data02_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"27\": [], \"32\": [], \"37\": [], \"42\": [], \"47\": []}"}], "video": "data02_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"28\": [], \"33\": [], \"38\": [], \"43\": [], \"48\": []}"}], "video": "data02_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"29\": [], \"34\": [], \"39\": [], \"44\": [], \"49\": []}"}], "video": "data02_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"30\": [], \"35\": [], \"40\": [], \"45\": [], \"50\": []}"}], "video": "data02_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"51\": [], \"56\": [], \"61\": [], \"66\": [], \"71\": []}"}], "video": "data02_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"52\": [], \"57\": [], \"62\": [], \"67\": [], \"72\": []}"}], "video": "data02_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"53\": [], \"58\": [], \"63\": [], \"68\": [], \"73\": []}"}], "video": "data02_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"54\": [], \"59\": [], \"64\": [], \"69\": [], \"74\": []}"}], "video": "data02_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"55\": [], \"60\": [], \"65\": [], \"70\": [], \"75\": []}"}], "video": "data02_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"76\": [], \"81\": [], \"86\": [], \"91\": [], \"96\": []}"}], "video": "data02_seq_015.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"77\": [], \"82\": [], \"87\": [], \"92\": [], \"97\": []}"}], "video": "data02_seq_016.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"78\": [], \"83\": [], \"88\": [], \"93\": [], \"98\": []}"}], "video": "data02_seq_017.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"79\": [], \"84\": [], \"89\": [], \"94\": [], \"99\": []}"}], "video": "data02_seq_018.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"80\": [], \"85\": [], \"90\": [], \"95\": [], \"100\": []}"}], "video": "data02_seq_019.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"101\": [], \"106\": [], \"111\": [], \"116\": [], \"121\": []}"}], "video": "data02_seq_020.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"102\": [], \"107\": [], \"112\": [], \"117\": [], \"122\": []}"}], "video": "data02_seq_021.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"103\": [], \"108\": [], \"113\": [], \"118\": [], \"123\": []}"}], "video": "data02_seq_022.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"104\": [], \"109\": [], \"114\": [], \"119\": [], \"124\": []}"}], "video": "data02_seq_023.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"105\": [], \"110\": [], \"115\": [], \"120\": [], \"125\": []}"}], "video": "data02_seq_024.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"126\": [], \"131\": [], \"136\": [], \"141\": [], \"146\": []}"}], "video": "data02_seq_025.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"127\": [], \"132\": [], \"137\": [], \"142\": [], \"147\": []}"}], "video": "data02_seq_026.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"128\": [], \"133\": [], \"138\": [], \"143\": [], \"148\": []}"}], "video": "data02_seq_027.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"129\": [], \"134\": [], \"139\": [], \"144\": [], \"149\": []}"}], "video": "data02_seq_028.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"130\": [], \"135\": [], \"140\": [], \"145\": [], \"150\": []}"}], "video": "data02_seq_029.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"1\": [], \"6\": [], \"11\": [], \"16\": [], \"21\": []}"}], "video": "data04_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"2\": [], \"7\": [], \"12\": [], \"17\": [], \"22\": []}"}], "video": "data04_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"3\": [], \"8\": [], \"13\": [], \"18\": [], \"23\": []}"}], "video": "data04_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"4\": [], \"9\": [], \"14\": [], \"19\": [], \"24\": []}"}], "video": "data04_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"5\": [], \"10\": [], \"15\": [], \"20\": [], \"25\": []}"}], "video": "data04_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"26\": [], \"31\": [], \"36\": [], \"41\": [], \"46\": []}"}], "video": "data04_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"27\": [], \"32\": [], \"37\": [], \"42\": [], \"47\": []}"}], "video": "data04_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"28\": [], \"33\": [], \"38\": [], \"43\": [], \"48\": []}"}], "video": "data04_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"29\": [], \"34\": [], \"39\": [], \"44\": [], \"49\": []}"}], "video": "data04_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"30\": [], \"35\": [], \"40\": [], \"45\": [], \"50\": []}"}], "video": "data04_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"51\": [], \"56\": [], \"61\": [], \"66\": [], \"71\": []}"}], "video": "data04_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"52\": [], \"57\": [], \"62\": [], \"67\": [], \"72\": []}"}], "video": "data04_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"53\": [], \"58\": [], \"63\": [], \"68\": [], \"73\": []}"}], "video": "data04_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"54\": [], \"59\": [], \"64\": [], \"69\": [], \"74\": []}"}], "video": "data04_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"55\": [], \"60\": [], \"65\": [], \"70\": [], \"75\": []}"}], "video": "data04_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"1\": [], \"6\": [], \"11\": [], \"16\": [], \"21\": []}"}], "video": "data05_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"2\": [], \"7\": [], \"12\": [], \"17\": [], \"22\": []}"}], "video": "data05_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"3\": [], \"8\": [], \"13\": [], \"18\": [], \"23\": []}"}], "video": "data05_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"4\": [], \"9\": [], \"14\": [], \"19\": [], \"24\": []}"}], "video": "data05_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"5\": [], \"10\": [], \"15\": [], \"20\": [], \"25\": []}"}], "video": "data05_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"26\": [], \"31\": [], \"36\": [], \"41\": [], \"46\": []}"}], "video": "data05_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"27\": [], \"32\": [], \"37\": [], \"42\": [], \"47\": []}"}], "video": "data05_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"28\": [], \"33\": [], \"38\": [], \"43\": [], \"48\": []}"}], "video": "data05_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"29\": [], \"34\": [], \"39\": [], \"44\": [], \"49\": []}"}], "video": "data05_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"30\": [], \"35\": [], \"40\": [], \"45\": [], \"50\": []}"}], "video": "data05_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"51\": [], \"56\": [], \"61\": [], \"66\": [], \"71\": []}"}], "video": "data05_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"52\": [], \"57\": [], \"62\": [], \"67\": [], \"72\": []}"}], "video": "data05_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"53\": [], \"58\": [], \"63\": [], \"68\": [], \"73\": []}"}], "video": "data05_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"54\": [], \"59\": [], \"64\": [], \"69\": [], \"74\": []}"}], "video": "data05_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"55\": [], \"60\": [], \"65\": [], \"70\": [], \"75\": []}"}], "video": "data05_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"1\": [], \"6\": [], \"11\": [], \"16\": [], \"21\": []}"}], "video": "data06_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"2\": [], \"7\": [], \"12\": [], \"17\": [], \"22\": []}"}], "video": "data06_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"3\": [], \"8\": [], \"13\": [], \"18\": [], \"23\": []}"}], "video": "data06_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"4\": [], \"9\": [], \"14\": [], \"19\": [], \"24\": []}"}], "video": "data06_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"5\": [], \"10\": [], \"15\": [], \"20\": [], \"25\": []}"}], "video": "data06_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"26\": [], \"31\": [], \"36\": [], \"41\": [], \"46\": []}"}], "video": "data06_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"27\": [], \"32\": [], \"37\": [], \"42\": [], \"47\": []}"}], "video": "data06_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"28\": [], \"33\": [], \"38\": [], \"43\": [], \"48\": []}"}], "video": "data06_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"29\": [], \"34\": [], \"39\": [], \"44\": [], \"49\": []}"}], "video": "data06_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"30\": [], \"35\": [], \"40\": [], \"45\": [], \"50\": []}"}], "video": "data06_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"51\": [], \"56\": [], \"61\": [], \"66\": [], \"71\": []}"}], "video": "data06_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"52\": [], \"57\": [], \"62\": [], \"67\": [], \"72\": []}"}], "video": "data06_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"53\": [], \"58\": [], \"63\": [], \"68\": [], \"73\": []}"}], "video": "data06_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"54\": [], \"59\": [], \"64\": [], \"69\": [], \"74\": []}"}], "video": "data06_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"55\": [], \"60\": [], \"65\": [], \"70\": [], \"75\": []}"}], "video": "data06_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"1\": [], \"6\": [], \"11\": [], \"16\": [], \"21\": []}"}], "video": "data07_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"2\": [], \"7\": [], \"12\": [], \"17\": [], \"22\": []}"}], "video": "data07_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"3\": [], \"8\": [], \"13\": [], \"18\": [], \"23\": []}"}], "video": "data07_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"4\": [], \"9\": [], \"14\": [], \"19\": [], \"24\": []}"}], "video": "data07_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"5\": [], \"10\": [], \"15\": [], \"20\": [], \"25\": []}"}], "video": "data07_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"26\": [], \"31\": [], \"36\": [], \"41\": [], \"46\": []}"}], "video": "data07_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"27\": [], \"32\": [], \"37\": [], \"42\": [], \"47\": []}"}], "video": "data07_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"28\": [], \"33\": [], \"38\": [], \"43\": [], \"48\": []}"}], "video": "data07_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"29\": [], \"34\": [], \"39\": [], \"44\": [], \"49\": []}"}], "video": "data07_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"30\": [], \"35\": [], \"40\": [], \"45\": [], \"50\": []}"}], "video": "data07_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"51\": [], \"56\": [], \"61\": [], \"66\": [], \"71\": []}"}], "video": "data07_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"52\": [], \"57\": [], \"62\": [], \"67\": [], \"72\": []}"}], "video": "data07_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"53\": [], \"58\": [], \"63\": [], \"68\": [], \"73\": []}"}], "video": "data07_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"54\": [], \"59\": [], \"64\": [], \"69\": [], \"74\": []}"}], "video": "data07_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"55\": [], \"60\": [], \"65\": [], \"70\": [], \"75\": []}"}], "video": "data07_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"1\": [], \"6\": [], \"11\": [], \"16\": [], \"21\": []}"}], "video": "data25_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"2\": [], \"7\": [], \"12\": [], \"17\": [], \"22\": []}"}], "video": "data25_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"3\": [], \"8\": [], \"13\": [], \"18\": [], \"23\": []}"}], "video": "data25_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"4\": [], \"9\": [], \"14\": [], \"19\": [], \"24\": []}"}], "video": "data25_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"5\": [], \"10\": [], \"15\": [], \"20\": [], \"25\": []}"}], "video": "data25_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"26\": [], \"31\": [], \"36\": [], \"41\": [], \"46\": []}"}], "video": "data25_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"27\": [], \"32\": [], \"37\": [], \"42\": [], \"47\": []}"}], "video": "data25_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"28\": [], \"33\": [], \"38\": [], \"43\": [], \"48\": []}"}], "video": "data25_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"29\": [], \"34\": [], \"39\": [], \"44\": [], \"49\": []}"}], "video": "data25_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"30\": [], \"35\": [], \"40\": [], \"45\": [], \"50\": []}"}], "video": "data25_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"51\": [], \"56\": [], \"61\": [], \"66\": [], \"71\": []}"}], "video": "data25_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"52\": [], \"57\": [], \"62\": [], \"67\": [], \"72\": []}"}], "video": "data25_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"53\": [], \"58\": [], \"63\": [], \"68\": [], \"73\": []}"}], "video": "data25_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"54\": [], \"59\": [], \"64\": [], \"69\": [], \"74\": []}"}], "video": "data25_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"55\": [], \"60\": [], \"65\": [], \"70\": [], \"75\": []}"}], "video": "data25_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"76\": [], \"81\": [], \"86\": [], \"91\": [], \"96\": []}"}], "video": "data25_seq_015.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"77\": [], \"82\": [], \"87\": [], \"92\": [], \"97\": []}"}], "video": "data25_seq_016.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"78\": [], \"83\": [], \"88\": [], \"93\": [], \"98\": []}"}], "video": "data25_seq_017.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"79\": [], \"84\": [], \"89\": [], \"94\": [], \"99\": []}"}], "video": "data25_seq_018.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"80\": [], \"85\": [], \"90\": [], \"95\": [], \"100\": []}"}], "video": "data25_seq_019.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"101\": [], \"106\": [], \"111\": [], \"116\": [], \"121\": []}"}], "video": "data25_seq_020.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"102\": [], \"107\": [], \"112\": [], \"117\": [], \"122\": []}"}], "video": "data25_seq_021.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"103\": [], \"108\": [], \"113\": [], \"118\": [], \"123\": []}"}], "video": "data25_seq_022.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"104\": [], \"109\": [], \"114\": [], \"119\": [], \"124\": []}"}], "video": "data25_seq_023.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"105\": [], \"110\": [], \"115\": [], \"120\": [], \"125\": []}"}], "video": "data25_seq_024.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"126\": [], \"131\": [], \"136\": [], \"141\": [], \"146\": []}"}], "video": "data25_seq_025.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"127\": [], \"132\": [], \"137\": [], \"142\": [], \"147\": []}"}], "video": "data25_seq_026.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"128\": [], \"133\": [], \"138\": [], \"143\": [], \"148\": []}"}], "video": "data25_seq_027.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"129\": [], \"134\": [], \"139\": [], \"144\": [], \"149\": []}"}], "video": "data25_seq_028.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"130\": [], \"135\": [], \"140\": [], \"145\": [], \"150\": []}"}], "video": "data25_seq_029.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"151\": [], \"156\": [], \"161\": [], \"166\": [], \"171\": []}"}], "video": "data25_seq_030.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"152\": [], \"157\": [], \"162\": [], \"167\": [], \"172\": []}"}], "video": "data25_seq_031.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"153\": [], \"158\": [], \"163\": [], \"168\": [], \"173\": []}"}], "video": "data25_seq_032.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"154\": [], \"159\": [], \"164\": [], \"169\": [], \"174\": []}"}], "video": "data25_seq_033.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"155\": [], \"160\": [], \"165\": [], \"170\": [], \"175\": []}"}], "video": "data25_seq_034.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"176\": [], \"181\": [], \"186\": [], \"191\": [], \"196\": []}"}], "video": "data25_seq_035.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"177\": [], \"182\": [], \"187\": [], \"192\": [], \"197\": []}"}], "video": "data25_seq_036.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"178\": [], \"183\": [], \"188\": [], \"193\": [], \"198\": []}"}], "video": "data25_seq_037.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"179\": [], \"184\": [], \"189\": [], \"194\": [], \"199\": []}"}], "video": "data25_seq_038.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"180\": [], \"185\": [], \"190\": [], \"195\": [], \"200\": []}"}], "video": "data25_seq_039.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"201\": [], \"206\": [], \"211\": [], \"216\": [], \"221\": []}"}], "video": "data25_seq_040.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"202\": [], \"207\": [], \"212\": [], \"217\": [], \"222\": []}"}], "video": "data25_seq_041.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"203\": [], \"208\": [], \"213\": [], \"218\": [], \"223\": []}"}], "video": "data25_seq_042.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"204\": [], \"209\": [], \"214\": [], \"219\": [], \"224\": []}"}], "video": "data25_seq_043.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"205\": [], \"210\": [], \"215\": [], \"220\": [], \"225\": []}"}], "video": "data25_seq_044.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"226\": [], \"231\": [], \"236\": [], \"241\": [], \"246\": []}"}], "video": "data25_seq_045.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"227\": [], \"232\": [], \"237\": [], \"242\": [], \"247\": []}"}], "video": "data25_seq_046.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"228\": [], \"233\": [], \"238\": [], \"243\": [], \"248\": []}"}], "video": "data25_seq_047.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"229\": [], \"234\": [], \"239\": [], \"244\": [], \"249\": []}"}], "video": "data25_seq_048.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"230\": [], \"235\": [], \"240\": [], \"245\": [], \"250\": []}"}], "video": "data25_seq_049.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"251\": [], \"256\": [], \"261\": [], \"266\": [], \"271\": []}"}], "video": "data25_seq_050.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"252\": [], \"257\": [], \"262\": [], \"267\": [], \"272\": []}"}], "video": "data25_seq_051.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"253\": [], \"258\": [], \"263\": [], \"268\": [], \"273\": []}"}], "video": "data25_seq_052.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"254\": [], \"259\": [], \"264\": [], \"269\": [], \"274\": []}"}], "video": "data25_seq_053.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"255\": [], \"260\": [], \"265\": [], \"270\": [], \"275\": []}"}], "video": "data25_seq_054.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"276\": [], \"281\": [], \"286\": [], \"291\": [], \"296\": []}"}], "video": "data25_seq_055.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"277\": [], \"282\": [], \"287\": [], \"292\": [], \"297\": []}"}], "video": "data25_seq_056.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"278\": [], \"283\": [], \"288\": [], \"293\": [], \"298\": []}"}], "video": "data25_seq_057.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"279\": [], \"284\": [], \"289\": [], \"294\": [], \"299\": []}"}], "video": "data25_seq_058.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"280\": [], \"285\": [], \"290\": [], \"295\": [], \"300\": []}"}], "video": "data25_seq_059.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"1\": [], \"6\": [], \"11\": [], \"16\": [], \"21\": []}"}], "video": "data26_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"2\": [], \"7\": [], \"12\": [], \"17\": [], \"22\": []}"}], "video": "data26_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"3\": [], \"8\": [], \"13\": [], \"18\": [], \"23\": []}"}], "video": "data26_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"4\": [], \"9\": [], \"14\": [], \"19\": [], \"24\": []}"}], "video": "data26_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"5\": [], \"10\": [], \"15\": [], \"20\": [], \"25\": []}"}], "video": "data26_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"26\": [], \"31\": [], \"36\": [], \"41\": [], \"46\": []}"}], "video": "data26_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"27\": [], \"32\": [], \"37\": [], \"42\": [], \"47\": []}"}], "video": "data26_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"28\": [], \"33\": [], \"38\": [], \"43\": [], \"48\": []}"}], "video": "data26_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"29\": [], \"34\": [], \"39\": [], \"44\": [], \"49\": []}"}], "video": "data26_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"30\": [], \"35\": [], \"40\": [], \"45\": [], \"50\": []}"}], "video": "data26_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"51\": [], \"56\": [], \"61\": [], \"66\": [], \"71\": []}"}], "video": "data26_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"52\": [], \"57\": [], \"62\": [], \"67\": [], \"72\": []}"}], "video": "data26_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"53\": [], \"58\": [], \"63\": [], \"68\": [], \"73\": []}"}], "video": "data26_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"54\": [], \"59\": [], \"64\": [], \"69\": [], \"74\": []}"}], "video": "data26_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"55\": [], \"60\": [], \"65\": [], \"70\": [], \"75\": []}"}], "video": "data26_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"76\": [], \"81\": [], \"86\": [], \"91\": [], \"96\": []}"}], "video": "data26_seq_015.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"77\": [], \"82\": [], \"87\": [], \"92\": [], \"97\": []}"}], "video": "data26_seq_016.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"78\": [], \"83\": [], \"88\": [], \"93\": [], \"98\": []}"}], "video": "data26_seq_017.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"79\": [], \"84\": [], \"89\": [], \"94\": [], \"99\": []}"}], "video": "data26_seq_018.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"80\": [], \"85\": [], \"90\": [], \"95\": [], \"100\": []}"}], "video": "data26_seq_019.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"101\": [], \"106\": [], \"111\": [], \"116\": [], \"121\": []}"}], "video": "data26_seq_020.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"102\": [], \"107\": [], \"112\": [], \"117\": [], \"122\": []}"}], "video": "data26_seq_021.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"103\": [], \"108\": [], \"113\": [], \"118\": [], \"123\": []}"}], "video": "data26_seq_022.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"104\": [], \"109\": [], \"114\": [], \"119\": [], \"124\": []}"}], "video": "data26_seq_023.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"105\": [], \"110\": [], \"115\": [], \"120\": [], \"125\": []}"}], "video": "data26_seq_024.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"126\": [], \"131\": [], \"136\": [], \"141\": [], \"146\": []}"}], "video": "data26_seq_025.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"127\": [], \"132\": [], \"137\": [], \"142\": [], \"147\": []}"}], "video": "data26_seq_026.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"128\": [], \"133\": [], \"138\": [], \"143\": [], \"148\": []}"}], "video": "data26_seq_027.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"129\": [], \"134\": [], \"139\": [], \"144\": [], \"149\": []}"}], "video": "data26_seq_028.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"130\": [], \"135\": [], \"140\": [], \"145\": [], \"150\": []}"}], "video": "data26_seq_029.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"151\": [], \"156\": [], \"161\": [], \"166\": [], \"171\": []}"}], "video": "data26_seq_030.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"152\": [], \"157\": [], \"162\": [], \"167\": [], \"172\": []}"}], "video": "data26_seq_031.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"153\": [], \"158\": [], \"163\": [], \"168\": [], \"173\": []}"}], "video": "data26_seq_032.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"154\": [], \"159\": [], \"164\": [], \"169\": [], \"174\": []}"}], "video": "data26_seq_033.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"155\": [], \"160\": [], \"165\": [], \"170\": [], \"175\": []}"}], "video": "data26_seq_034.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"176\": [], \"181\": [], \"186\": [], \"191\": [], \"196\": []}"}], "video": "data26_seq_035.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"177\": [], \"182\": [], \"187\": [], \"192\": [], \"197\": []}"}], "video": "data26_seq_036.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"178\": [], \"183\": [], \"188\": [], \"193\": [], \"198\": []}"}], "video": "data26_seq_037.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"179\": [], \"184\": [], \"189\": [], \"194\": [], \"199\": []}"}], "video": "data26_seq_038.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"180\": [], \"185\": [], \"190\": [], \"195\": [], \"200\": []}"}], "video": "data26_seq_039.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"201\": [], \"206\": [], \"211\": [], \"216\": [], \"221\": []}"}], "video": "data26_seq_040.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"202\": [], \"207\": [], \"212\": [], \"217\": [], \"222\": []}"}], "video": "data26_seq_041.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"203\": [], \"208\": [], \"213\": [], \"218\": [], \"223\": []}"}], "video": "data26_seq_042.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"204\": [], \"209\": [], \"214\": [], \"219\": [], \"224\": []}"}], "video": "data26_seq_043.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"205\": [], \"210\": [], \"215\": [], \"220\": [], \"225\": []}"}], "video": "data26_seq_044.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"226\": [], \"231\": [], \"236\": [], \"241\": [], \"246\": []}"}], "video": "data26_seq_045.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"227\": [], \"232\": [], \"237\": [], \"242\": [], \"247\": []}"}], "video": "data26_seq_046.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"228\": [], \"233\": [], \"238\": [], \"243\": [], \"248\": []}"}], "video": "data26_seq_047.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"229\": [], \"234\": [], \"239\": [], \"244\": [], \"249\": []}"}], "video": "data26_seq_048.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"230\": [], \"235\": [], \"240\": [], \"245\": [], \"250\": []}"}], "video": "data26_seq_049.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"251\": [], \"256\": [], \"261\": [], \"266\": [], \"271\": []}"}], "video": "data26_seq_050.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"252\": [], \"257\": [], \"262\": [], \"267\": [], \"272\": []}"}], "video": "data26_seq_051.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"253\": [], \"258\": [], \"263\": [], \"268\": [], \"273\": []}"}], "video": "data26_seq_052.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"254\": [], \"259\": [], \"264\": [], \"269\": [], \"274\": []}"}], "video": "data26_seq_053.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"255\": [], \"260\": [], \"265\": [], \"270\": [], \"275\": []}"}], "video": "data26_seq_054.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"276\": [], \"281\": [], \"286\": [], \"291\": [], \"296\": []}"}], "video": "data26_seq_055.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"277\": [], \"282\": [], \"287\": [], \"292\": [], \"297\": []}"}], "video": "data26_seq_056.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"278\": [], \"283\": [], \"288\": [], \"293\": [], \"298\": []}"}], "video": "data26_seq_057.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"279\": [], \"284\": [], \"289\": [], \"294\": [], \"299\": []}"}], "video": "data26_seq_058.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。\n\n任务要求：\n1. 检测每帧中的无人机目标（通常表现为小的亮点）\n2. 利用视频的时序信息确认目标的真实性\n3. 观察目标在不同帧中的位置变化\n4. 精确定位目标的边界框\n\n检测重点：\n- 无人机目标通常是小的白色或亮色斑点\n- 目标会在帧间发生位置变化\n- 排除静态的背景噪声\n- 提供精确的边界框坐标\n\n请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：\n```json\n{\n  \"frame_name_1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_name_2\": [...]\n}\n```\n\n注意：\n- bbox格式为[x1, y1, x2, y2]，使用像素坐标\n- class_name统一使用\"drone\"\n- confidence表示检测置信度\n- 如果某帧没有目标，返回空列表[]"}, {"from": "gpt", "value": "{\"280\": [], \"285\": [], \"290\": [], \"295\": [], \"300\": []}"}], "video": "data26_seq_059.mp4"}]