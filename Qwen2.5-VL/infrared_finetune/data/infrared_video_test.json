[{"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"1\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"6\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"11\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"16\": [\n    {\n      \"bbox\": [69, 106, 74, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"21\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"2\": [\n    {\n      \"bbox\": [67, 106, 72, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"7\": [\n    {\n      \"bbox\": [65, 107, 70, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"12\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"17\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"22\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"3\": [\n    {\n      \"bbox\": [66, 106, 71, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"8\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"13\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"18\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"23\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"4\": [\n    {\n      \"bbox\": [67, 106, 72, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"9\": [\n    {\n      \"bbox\": [63, 106, 68, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"14\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"19\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"24\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"5\": [\n    {\n      \"bbox\": [66, 106, 71, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"10\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"15\": [\n    {\n      \"bbox\": [70, 106, 75, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"20\": [\n    {\n      \"bbox\": [67, 106, 72, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"25\": [\n    {\n      \"bbox\": [63, 106, 68, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"26\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"31\": [\n    {\n      \"bbox\": [70, 106, 75, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"36\": [\n    {\n      \"bbox\": [67, 106, 72, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"41\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"46\": [\n    {\n      \"bbox\": [66, 106, 71, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"27\": [\n    {\n      \"bbox\": [61, 106, 66, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"32\": [\n    {\n      \"bbox\": [70, 106, 75, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"37\": [\n    {\n      \"bbox\": [66, 106, 71, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"42\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"47\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"28\": [\n    {\n      \"bbox\": [61, 107, 66, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"33\": [\n    {\n      \"bbox\": [69, 106, 74, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"38\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"43\": [\n    {\n      \"bbox\": [60, 105, 65, 110],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"48\": [\n    {\n      \"bbox\": [57, 106, 62, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"29\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"34\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"39\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"44\": [\n    {\n      \"bbox\": [60, 106, 65, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"49\": [\n    {\n      \"bbox\": [56, 106, 61, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"30\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"35\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"40\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"45\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"50\": [\n    {\n      \"bbox\": [55, 107, 60, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"51\": [\n    {\n      \"bbox\": [55, 106, 60, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"56\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"61\": [\n    {\n      \"bbox\": [57, 106, 62, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"66\": [\n    {\n      \"bbox\": [71, 107, 76, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"71\": [\n    {\n      \"bbox\": [66, 107, 71, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"52\": [\n    {\n      \"bbox\": [54, 106, 59, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"57\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"62\": [\n    {\n      \"bbox\": [58, 107, 63, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"67\": [\n    {\n      \"bbox\": [70, 107, 75, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"72\": [\n    {\n      \"bbox\": [65, 108, 70, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"53\": [\n    {\n      \"bbox\": [56, 106, 61, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"58\": [\n    {\n      \"bbox\": [59, 107, 64, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"63\": [\n    {\n      \"bbox\": [62, 107, 67, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"68\": [\n    {\n      \"bbox\": [68, 108, 73, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"73\": [\n    {\n      \"bbox\": [65, 107, 70, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"54\": [\n    {\n      \"bbox\": [58, 106, 63, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"59\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"64\": [\n    {\n      \"bbox\": [68, 107, 73, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"69\": [\n    {\n      \"bbox\": [68, 108, 73, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"74\": [\n    {\n      \"bbox\": [63, 107, 68, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"55\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"60\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"65\": [\n    {\n      \"bbox\": [70, 107, 75, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"70\": [\n    {\n      \"bbox\": [67, 107, 72, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"75\": [\n    {\n      \"bbox\": [62, 108, 67, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"76\": [\n    {\n      \"bbox\": [62, 108, 67, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"81\": [\n    {\n      \"bbox\": [66, 109, 71, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"86\": [\n    {\n      \"bbox\": [65, 108, 70, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"91\": [\n    {\n      \"bbox\": [62, 109, 67, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"96\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_015.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"77\": [\n    {\n      \"bbox\": [61, 108, 66, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"82\": [\n    {\n      \"bbox\": [66, 109, 71, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"87\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"92\": [\n    {\n      \"bbox\": [62, 109, 67, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"97\": [\n    {\n      \"bbox\": [69, 110, 74, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_016.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"78\": [\n    {\n      \"bbox\": [61, 108, 66, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"83\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"88\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"93\": [\n    {\n      \"bbox\": [60, 109, 65, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"98\": [\n    {\n      \"bbox\": [69, 109, 74, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_017.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"79\": [\n    {\n      \"bbox\": [62, 108, 67, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"84\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"89\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"94\": [\n    {\n      \"bbox\": [61, 109, 66, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"99\": [\n    {\n      \"bbox\": [69, 110, 74, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_018.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"80\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"85\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"90\": [\n    {\n      \"bbox\": [62, 109, 67, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"95\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"100\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_019.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"101\": [\n    {\n      \"bbox\": [67, 110, 72, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"106\": [\n    {\n      \"bbox\": [64, 110, 69, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"111\": [\n    {\n      \"bbox\": [63, 109, 68, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"116\": [\n    {\n      \"bbox\": [67, 109, 72, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"121\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_020.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"102\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"107\": [\n    {\n      \"bbox\": [62, 110, 67, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"112\": [\n    {\n      \"bbox\": [66, 109, 71, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"117\": [\n    {\n      \"bbox\": [66, 109, 71, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"122\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_021.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"103\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"108\": [\n    {\n      \"bbox\": [62, 109, 67, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"113\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"118\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"123\": [\n    {\n      \"bbox\": [63, 109, 68, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_022.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"104\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"109\": [\n    {\n      \"bbox\": [61, 109, 66, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"114\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"119\": [\n    {\n      \"bbox\": [65, 108, 70, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"124\": [\n    {\n      \"bbox\": [63, 109, 68, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_023.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"105\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"110\": [\n    {\n      \"bbox\": [61, 109, 66, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"115\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"120\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"125\": [\n    {\n      \"bbox\": [62, 110, 67, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_024.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"126\": [\n    {\n      \"bbox\": [62, 110, 67, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"131\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"136\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"141\": [\n    {\n      \"bbox\": [63, 109, 68, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"146\": [\n    {\n      \"bbox\": [70, 109, 75, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_025.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"127\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"132\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"137\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"142\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"147\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_026.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"128\": [\n    {\n      \"bbox\": [69, 110, 74, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"133\": [\n    {\n      \"bbox\": [70, 110, 75, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"138\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"143\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"148\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_027.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"129\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"134\": [\n    {\n      \"bbox\": [69, 110, 74, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"139\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"144\": [\n    {\n      \"bbox\": [67, 109, 72, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"149\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_028.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"130\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"135\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"140\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"145\": [\n    {\n      \"bbox\": [69, 109, 74, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"150\": [\n    {\n      \"bbox\": [67, 110, 72, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_029.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"151\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"156\": [\n    {\n      \"bbox\": [63, 110, 68, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"161\": [\n    {\n      \"bbox\": [73, 110, 78, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"166\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"171\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_030.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"152\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"157\": [\n    {\n      \"bbox\": [62, 110, 67, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"162\": [\n    {\n      \"bbox\": [72, 110, 77, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"167\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"172\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_031.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"153\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"158\": [\n    {\n      \"bbox\": [63, 110, 68, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"163\": [\n    {\n      \"bbox\": [72, 110, 77, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"168\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"173\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_032.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"154\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"159\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"164\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"169\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"174\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_033.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"155\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"160\": [\n    {\n      \"bbox\": [70, 110, 75, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"165\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"170\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"175\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_034.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"176\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"181\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"186\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"191\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"196\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_035.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"177\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"182\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"187\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"192\": [\n    {\n      \"bbox\": [63, 111, 68, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"197\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_036.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"178\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"183\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"188\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"193\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"198\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_037.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"179\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"184\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"189\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"194\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"199\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_038.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"180\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"185\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"190\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"195\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"200\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_039.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"201\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"206\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"211\": [\n    {\n      \"bbox\": [75, 111, 80, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"216\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"221\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_040.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"202\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"207\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"212\": [\n    {\n      \"bbox\": [74, 110, 79, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"217\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"222\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_041.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"203\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"208\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"213\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"218\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"223\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_042.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"204\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"209\": [\n    {\n      \"bbox\": [77, 111, 82, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"214\": [\n    {\n      \"bbox\": [74, 110, 79, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"219\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"224\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_043.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"205\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"210\": [\n    {\n      \"bbox\": [76, 111, 81, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"215\": [\n    {\n      \"bbox\": [73, 111, 78, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"220\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"225\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_044.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"226\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"231\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"236\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"241\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"246\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_045.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"227\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"232\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"237\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"242\": [\n    {\n      \"bbox\": [72, 111, 77, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"247\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_046.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"228\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"233\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"238\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"243\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"248\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_047.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"229\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"234\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"239\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"244\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"249\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_048.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"230\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"235\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"240\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"245\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"250\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_049.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"251\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"256\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"261\": [\n    {\n      \"bbox\": [73, 112, 78, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"266\": [\n    {\n      \"bbox\": [69, 112, 74, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"271\": [\n    {\n      \"bbox\": [69, 112, 74, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_050.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"252\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"257\": [\n    {\n      \"bbox\": [73, 111, 78, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"262\": [\n    {\n      \"bbox\": [72, 112, 77, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"267\": [\n    {\n      \"bbox\": [69, 112, 74, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"272\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_051.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"253\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"258\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"263\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"268\": [\n    {\n      \"bbox\": [68, 112, 73, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"273\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_052.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"254\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"259\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"264\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"269\": [\n    {\n      \"bbox\": [68, 112, 73, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"274\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_053.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"255\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"260\": [\n    {\n      \"bbox\": [73, 112, 78, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"265\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"270\": [\n    {\n      \"bbox\": [68, 112, 73, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"275\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_054.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"276\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"281\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"286\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"291\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"296\": [\n    {\n      \"bbox\": [73, 112, 78, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_055.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"277\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"282\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"287\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"292\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"297\": [\n    {\n      \"bbox\": [73, 112, 78, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_056.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"278\": [\n    {\n      \"bbox\": [72, 111, 77, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"283\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"288\": [\n    {\n      \"bbox\": [73, 111, 78, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"293\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"298\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_057.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"279\": [\n    {\n      \"bbox\": [73, 111, 78, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"284\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"289\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"294\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"299\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_058.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"280\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"285\": [\n    {\n      \"bbox\": [68, 112, 73, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"290\": [\n    {\n      \"bbox\": [75, 112, 80, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"295\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"300\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data01_seq_059.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"1\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"6\": [\n    {\n      \"bbox\": [348, 246, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"11\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"16\": [\n    {\n      \"bbox\": [351, 243, 357, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"21\": [\n    {\n      \"bbox\": [351, 244, 357, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_000.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"2\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"7\": [\n    {\n      \"bbox\": [349, 247, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"12\": [\n    {\n      \"bbox\": [349, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"17\": [\n    {\n      \"bbox\": [351, 243, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"22\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_001.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"3\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"8\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"13\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"18\": [\n    {\n      \"bbox\": [350, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"23\": [\n    {\n      \"bbox\": [349, 246, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_002.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"4\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"9\": [\n    {\n      \"bbox\": [349, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"14\": [\n    {\n      \"bbox\": [349, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"19\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"24\": [\n    {\n      \"bbox\": [349, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_003.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"5\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"10\": [\n    {\n      \"bbox\": [349, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"15\": [\n    {\n      \"bbox\": [351, 243, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"20\": [\n    {\n      \"bbox\": [349, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"25\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_004.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"26\": [\n    {\n      \"bbox\": [349, 246, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"31\": [\n    {\n      \"bbox\": [348, 246, 356, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"36\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"41\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"46\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_005.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"27\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"32\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"37\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"42\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"47\": [\n    {\n      \"bbox\": [348, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_006.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"28\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"33\": [\n    {\n      \"bbox\": [348, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"38\": [\n    {\n      \"bbox\": [348, 246, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"43\": [\n    {\n      \"bbox\": [348, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"48\": [\n    {\n      \"bbox\": [350, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_007.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"29\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"34\": [\n    {\n      \"bbox\": [348, 246, 356, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"39\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"44\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"49\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_008.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"30\": [\n    {\n      \"bbox\": [349, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"35\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"40\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"45\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"50\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_009.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"51\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"56\": [\n    {\n      \"bbox\": [350, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"61\": [\n    {\n      \"bbox\": [350, 246, 356, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"66\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"71\": [\n    {\n      \"bbox\": [348, 243, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_010.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"52\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"57\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"62\": [\n    {\n      \"bbox\": [348, 246, 355, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"67\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"72\": [\n    {\n      \"bbox\": [348, 241, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_011.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"53\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"58\": [\n    {\n      \"bbox\": [350, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"63\": [\n    {\n      \"bbox\": [348, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"68\": [\n    {\n      \"bbox\": [347, 244, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"73\": [\n    {\n      \"bbox\": [348, 241, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_012.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"54\": [\n    {\n      \"bbox\": [348, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"59\": [\n    {\n      \"bbox\": [350, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"64\": [\n    {\n      \"bbox\": [348, 246, 355, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"69\": [\n    {\n      \"bbox\": [347, 246, 355, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"74\": [\n    {\n      \"bbox\": [348, 242, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_013.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"55\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"60\": [\n    {\n      \"bbox\": [348, 246, 355, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"65\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"70\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"75\": [\n    {\n      \"bbox\": [350, 240, 356, 246],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_014.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"76\": [\n    {\n      \"bbox\": [348, 240, 355, 247],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"81\": [\n    {\n      \"bbox\": [348, 240, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"86\": [\n    {\n      \"bbox\": [348, 242, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"91\": [\n    {\n      \"bbox\": [351, 242, 357, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"96\": [\n    {\n      \"bbox\": [348, 241, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_015.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"77\": [\n    {\n      \"bbox\": [348, 240, 356, 247],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"82\": [\n    {\n      \"bbox\": [348, 239, 355, 245],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"87\": [\n    {\n      \"bbox\": [350, 242, 358, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"92\": [\n    {\n      \"bbox\": [351, 242, 357, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"97\": [\n    {\n      \"bbox\": [348, 240, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_016.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"78\": [\n    {\n      \"bbox\": [348, 239, 356, 245],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"83\": [\n    {\n      \"bbox\": [348, 240, 355, 247],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"88\": [\n    {\n      \"bbox\": [348, 243, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"93\": [\n    {\n      \"bbox\": [350, 241, 358, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"98\": [\n    {\n      \"bbox\": [348, 242, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_017.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"79\": [\n    {\n      \"bbox\": [348, 239, 355, 247],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"84\": [\n    {\n      \"bbox\": [348, 240, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"89\": [\n    {\n      \"bbox\": [351, 243, 357, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"94\": [\n    {\n      \"bbox\": [351, 243, 359, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"99\": [\n    {\n      \"bbox\": [348, 241, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_018.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"80\": [\n    {\n      \"bbox\": [348, 240, 355, 246],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"85\": [\n    {\n      \"bbox\": [348, 240, 355, 246],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"90\": [\n    {\n      \"bbox\": [351, 243, 359, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"95\": [\n    {\n      \"bbox\": [350, 243, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"100\": [\n    {\n      \"bbox\": [347, 241, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_019.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"101\": [\n    {\n      \"bbox\": [347, 241, 355, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"106\": [\n    {\n      \"bbox\": [347, 242, 355, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"111\": [\n    {\n      \"bbox\": [350, 242, 358, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"116\": [\n    {\n      \"bbox\": [351, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"121\": [\n    {\n      \"bbox\": [349, 244, 359, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_020.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"102\": [\n    {\n      \"bbox\": [348, 241, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"107\": [\n    {\n      \"bbox\": [347, 242, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"112\": [\n    {\n      \"bbox\": [348, 242, 358, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"117\": [\n    {\n      \"bbox\": [351, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"122\": [\n    {\n      \"bbox\": [349, 242, 359, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_021.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"103\": [\n    {\n      \"bbox\": [347, 242, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"108\": [\n    {\n      \"bbox\": [347, 241, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"113\": [\n    {\n      \"bbox\": [349, 241, 359, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"118\": [\n    {\n      \"bbox\": [352, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"123\": [\n    {\n      \"bbox\": [351, 245, 357, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_022.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"104\": [\n    {\n      \"bbox\": [346, 240, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"109\": [\n    {\n      \"bbox\": [347, 241, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"114\": [\n    {\n      \"bbox\": [349, 243, 359, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"119\": [\n    {\n      \"bbox\": [351, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"124\": [\n    {\n      \"bbox\": [351, 244, 357, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_023.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"105\": [\n    {\n      \"bbox\": [345, 242, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"110\": [\n    {\n      \"bbox\": [348, 242, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"115\": [\n    {\n      \"bbox\": [349, 242, 359, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"120\": [\n    {\n      \"bbox\": [351, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"125\": [\n    {\n      \"bbox\": [351, 244, 357, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_024.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"126\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"131\": [\n    {\n      \"bbox\": [348, 244, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"136\": [\n    {\n      \"bbox\": [348, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"141\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"146\": [\n    {\n      \"bbox\": [351, 245, 357, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_025.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"127\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"132\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"137\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"142\": [\n    {\n      \"bbox\": [348, 244, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"147\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_026.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"128\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"133\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"138\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"143\": [\n    {\n      \"bbox\": [350, 243, 358, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"148\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_027.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"129\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"134\": [\n    {\n      \"bbox\": [348, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"139\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"144\": [\n    {\n      \"bbox\": [351, 244, 357, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"149\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_028.mp4"}, {"conversations": [{"from": "human", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果：\n```json\n{\n  \"1\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"2\": [...]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者\n\n注意：这是视频序列分析，请仔细观察每帧中的微小目标，利用时序信息提高检测准确性。"}, {"from": "gpt", "value": "{\n  \"130\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"135\": [\n    {\n      \"bbox\": [348, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"140\": [\n    {\n      \"bbox\": [348, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"145\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"150\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data23_seq_029.mp4"}]