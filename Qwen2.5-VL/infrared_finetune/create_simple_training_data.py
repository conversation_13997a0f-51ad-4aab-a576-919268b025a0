#!/usr/bin/env python3
"""
创建简化的训练数据
只修改类别名称从target改为drone
"""

import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_simple_training_data():
    """创建简化的训练数据"""
    logger.info("🔧 创建简化的训练数据...")
    
    # 加载原始数据
    with open('data/infrared_video_train.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"原始数据样本数: {len(data)}")
    
    # 改进的提示词
    improved_prompt = """<video>
这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。

任务要求：
1. 检测每帧中的无人机目标（通常表现为小的亮点）
2. 利用视频的时序信息确认目标的真实性
3. 观察目标在不同帧中的位置变化
4. 精确定位目标的边界框

检测重点：
- 无人机目标通常是小的白色或亮色斑点
- 目标会在帧间发生位置变化
- 排除静态的背景噪声
- 提供精确的边界框坐标

请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：
```json
{
  "frame_name_1": [
    {
      "bbox": [x1, y1, x2, y2],
      "class_name": "drone",
      "confidence": 0.95
    }
  ],
  "frame_name_2": [...]
}
```

注意：
- bbox格式为[x1, y1, x2, y2]，使用像素坐标
- class_name统一使用"drone"
- confidence表示检测置信度
- 如果某帧没有目标，返回空列表[]"""
    
    fixed_data = []
    
    for i, sample in enumerate(data):
        try:
            # 获取原始对话
            conversations = sample.get('conversations', [])
            if len(conversations) < 2:
                continue
            
            # 获取原始回答
            gpt_response = conversations[1].get('value', '')
            
            # 解析JSON并修改类别
            try:
                response_data = json.loads(gpt_response)
                
                # 修改所有target为drone
                for frame_name, detections in response_data.items():
                    for detection in detections:
                        if detection.get('class_name') == 'target':
                            detection['class_name'] = 'drone'
                
                # 创建新的训练样本
                new_sample = {
                    'conversations': [
                        {
                            'from': 'human',
                            'value': improved_prompt
                        },
                        {
                            'from': 'gpt',
                            'value': json.dumps(response_data, ensure_ascii=False)
                        }
                    ],
                    'video': sample.get('video', '')
                }
                
                fixed_data.append(new_sample)
                
                if (i + 1) % 50 == 0:
                    logger.info(f"已处理 {i + 1}/{len(data)} 个样本")
                    
            except json.JSONDecodeError as e:
                logger.warning(f"样本 {i} JSON解析失败: {e}")
                continue
                
        except Exception as e:
            logger.error(f"处理样本 {i} 时出错: {e}")
            continue
    
    logger.info(f"修复后数据样本数: {len(fixed_data)}")
    
    # 保存修复后的数据
    output_file = "data/infrared_video_train_simple.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(fixed_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ 简化训练数据已保存到: {output_file}")
    
    # 显示样本
    if fixed_data:
        logger.info("📋 修复后的样本示例:")
        sample = fixed_data[0]
        logger.info(f"视频路径: {sample['video']}")
        
        gpt_response = sample['conversations'][1]['value']
        try:
            response_data = json.loads(gpt_response)
            logger.info("标准答案示例:")
            for frame_name, detections in list(response_data.items())[:2]:
                logger.info(f"  {frame_name}: {len(detections)} 个目标")
                if detections:
                    logger.info(f"    示例: {detections[0]}")
        except:
            pass
    
    return len(fixed_data)

def main():
    """主函数"""
    count = create_simple_training_data()
    logger.info(f"🎉 成功创建 {count} 个训练样本")

if __name__ == "__main__":
    main()
