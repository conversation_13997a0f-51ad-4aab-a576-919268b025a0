#!/usr/bin/env python3
"""
红外微调项目主控制脚本
整合数据预处理、微调训练、目标预测和评估的完整流程
"""

import os
import sys
import argparse
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('infrared_finetune.log')
    ]
)
logger = logging.getLogger(__name__)

class InfraredFinetuneManager:
    """红外微调项目管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.project_root = Path(__file__).parent
        
        # 确保必要的目录存在
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        dirs_to_create = [
            "data",
            "data/videos", 
            "output",
            "output/predictions",
            "output/evaluation"
        ]
        
        for dir_name in dirs_to_create:
            dir_path = self.project_root / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"确保目录存在: {dir_path}")
    
    def run_command(self, command: str, description: str):
        """运行命令"""
        logger.info(f"🚀 开始: {description}")
        
        # 构建完整的命令
        full_command = f"cd {self.project_root} && {command}"
        
        try:
            # 使用bash执行命令
            result = subprocess.run(
                ['bash', '-c', full_command],
                capture_output=True,
                text=True,
                check=True
            )
            
            logger.info(f"✅ 完成: {description}")
            if result.stdout:
                logger.info(f"输出: {result.stdout}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 失败: {description}")
            logger.error(f"错误: {e.stderr}")
            return False
    
    def step_1_data_preprocessing(self):
        """步骤1: 数据预处理"""
        logger.info("=" * 60)
        logger.info("步骤 1/4: 数据预处理")
        logger.info("=" * 60)
        
        command = "python data_processor.py"
        return self.run_command(command, "数据预处理")
    
    def step_2_training(self):
        """步骤2: 微调训练"""
        logger.info("=" * 60)
        logger.info("步骤 2/4: 微调训练")
        logger.info("=" * 60)
        
        command = "python train_fixed_frames.py"
        return self.run_command(command, "LoRA微调训练")
    
    def step_3_inference(self):
        """步骤3: 目标预测"""
        logger.info("=" * 60)
        logger.info("步骤 3/4: 高帧频弱小目标预测")
        logger.info("=" * 60)
        
        command = "python inference.py"
        return self.run_command(command, "目标预测")
    
    def step_4_evaluation(self):
        """步骤4: 评估"""
        logger.info("=" * 60)
        logger.info("步骤 4/4: 模型评估")
        logger.info("=" * 60)
        
        # 注意：评估需要真实标注数据，这里先跳过
        logger.info("⚠️ 评估步骤需要真实标注数据，当前跳过")
        logger.info("如需评估，请准备真实标注数据后运行: python evaluator.py")
        return True
    
    def run_full_pipeline(self):
        """运行完整流程"""
        logger.info("🎯 开始红外微调完整流程")
        logger.info(f"项目根目录: {self.project_root}")
        
        # 执行各个步骤
        steps = [
            ("数据预处理", self.step_1_data_preprocessing),
            ("微调训练", self.step_2_training),
            ("目标预测", self.step_3_inference),
            ("模型评估", self.step_4_evaluation)
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            try:
                if step_func():
                    success_count += 1
                    logger.info(f"✅ {step_name} 完成")
                else:
                    logger.error(f"❌ {step_name} 失败")
                    if self.config.get('stop_on_error', True):
                        logger.error("由于错误停止流程")
                        break
            except Exception as e:
                logger.error(f"❌ {step_name} 异常: {e}")
                if self.config.get('stop_on_error', True):
                    logger.error("由于异常停止流程")
                    break
        
        # 总结
        logger.info("=" * 60)
        logger.info("流程总结")
        logger.info("=" * 60)
        logger.info(f"完成步骤: {success_count}/{len(steps)}")
        
        if success_count == len(steps):
            logger.info("🎉 所有步骤完成!")
            self.print_results_summary()
        else:
            logger.warning("⚠️ 部分步骤未完成，请检查日志")
    
    def print_results_summary(self):
        """打印结果总结"""
        logger.info("\n📊 结果文件位置:")
        
        result_files = [
            ("训练数据", "data/infrared_video_train.json"),
            ("测试数据", "data/infrared_video_test.json"),
            ("微调模型", "output/infrared_lora/"),
            ("预测结果", "output/predictions/"),
            ("日志文件", "infrared_finetune.log")
        ]
        
        for desc, path in result_files:
            full_path = self.project_root / path
            if full_path.exists():
                logger.info(f"  ✅ {desc}: {full_path}")
            else:
                logger.info(f"  ❌ {desc}: {full_path} (不存在)")
    
    def run_single_step(self, step: str):
        """运行单个步骤"""
        step_map = {
            'preprocess': self.step_1_data_preprocessing,
            'train': self.step_2_training,
            'inference': self.step_3_inference,
            'evaluate': self.step_4_evaluation
        }
        
        if step not in step_map:
            logger.error(f"未知步骤: {step}")
            logger.info(f"可用步骤: {list(step_map.keys())}")
            return False
        
        return step_map[step]()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="红外微调项目主控制脚本")
    parser.add_argument(
        '--step', 
        choices=['preprocess', 'train', 'inference', 'evaluate', 'all'],
        default='all',
        help='要执行的步骤 (默认: all)'
    )
    parser.add_argument(
        '--stop-on-error',
        action='store_true',
        default=True,
        help='遇到错误时停止 (默认: True)'
    )
    parser.add_argument(
        '--config',
        type=str,
        help='配置文件路径 (可选)'
    )
    
    args = parser.parse_args()
    
    # 配置
    config = {
        'stop_on_error': args.stop_on_error,
    }
    
    # 如果提供了配置文件，加载它
    if args.config and os.path.exists(args.config):
        import json
        with open(args.config, 'r') as f:
            config.update(json.load(f))
    
    # 创建管理器
    manager = InfraredFinetuneManager(config)
    
    try:
        if args.step == 'all':
            manager.run_full_pipeline()
        else:
            success = manager.run_single_step(args.step)
            if success:
                logger.info(f"✅ 步骤 '{args.step}' 完成")
            else:
                logger.error(f"❌ 步骤 '{args.step}' 失败")
                sys.exit(1)
                
    except KeyboardInterrupt:
        logger.info("用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
