#!/usr/bin/env python3
"""
简单的梯度测试脚本
用于快速诊断梯度计算问题
"""

import torch
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_basic_gradients():
    """测试基础梯度计算"""
    logger.info("🧪 测试基础梯度计算...")
    
    # 确保梯度计算启用
    torch.set_grad_enabled(True)
    logger.info(f"torch.is_grad_enabled(): {torch.is_grad_enabled()}")
    
    # 创建需要梯度的张量
    x = torch.tensor([1.0, 2.0, 3.0], requires_grad=True)
    logger.info(f"x.requires_grad: {x.requires_grad}")
    
    # 简单计算
    y = x.sum()
    logger.info(f"y.requires_grad: {y.requires_grad}")
    logger.info(f"y.grad_fn: {y.grad_fn}")
    
    # 反向传播
    y.backward()
    logger.info(f"x.grad: {x.grad}")
    
    if x.grad is not None:
        logger.info("✅ 基础梯度计算正常")
        return True
    else:
        logger.error("❌ 基础梯度计算失败")
        return False

def test_tensor_operations():
    """测试张量操作中的梯度"""
    logger.info("🔧 测试张量操作梯度...")
    
    torch.set_grad_enabled(True)
    
    # 创建张量
    a = torch.randn(2, 3, requires_grad=True)
    b = torch.randn(2, 3, requires_grad=True)
    
    logger.info(f"a.requires_grad: {a.requires_grad}")
    logger.info(f"b.requires_grad: {b.requires_grad}")
    
    # 各种操作
    c = a + b
    d = torch.stack([a, b])
    e = torch.cat([a, b], dim=0)
    
    logger.info(f"c.requires_grad: {c.requires_grad}")
    logger.info(f"d.requires_grad: {d.requires_grad}")
    logger.info(f"e.requires_grad: {e.requires_grad}")
    
    # 计算损失
    loss = c.sum() + d.sum() + e.sum()
    logger.info(f"loss.requires_grad: {loss.requires_grad}")
    
    # 反向传播
    loss.backward()
    
    logger.info(f"a.grad is not None: {a.grad is not None}")
    logger.info(f"b.grad is not None: {b.grad is not None}")
    
    if a.grad is not None and b.grad is not None:
        logger.info("✅ 张量操作梯度正常")
        return True
    else:
        logger.error("❌ 张量操作梯度失败")
        return False

def test_model_like_scenario():
    """测试类似模型的场景"""
    logger.info("🤖 测试模型场景梯度...")
    
    torch.set_grad_enabled(True)
    
    # 模拟模型参数
    weight = torch.randn(10, 5, requires_grad=True)
    bias = torch.randn(10, requires_grad=True)
    
    logger.info(f"weight.requires_grad: {weight.requires_grad}")
    logger.info(f"bias.requires_grad: {bias.requires_grad}")
    
    # 模拟输入
    input_data = torch.randn(3, 5)  # 不需要梯度
    labels = torch.randint(0, 10, (3,))  # 不需要梯度
    
    # 前向传播
    output = torch.matmul(input_data, weight.t()) + bias
    logger.info(f"output.requires_grad: {output.requires_grad}")
    
    # 计算损失
    loss = torch.nn.functional.cross_entropy(output, labels)
    logger.info(f"loss.requires_grad: {loss.requires_grad}")
    logger.info(f"loss.grad_fn: {loss.grad_fn}")
    
    # 反向传播
    loss.backward()
    
    logger.info(f"weight.grad is not None: {weight.grad is not None}")
    logger.info(f"bias.grad is not None: {bias.grad is not None}")
    
    if weight.grad is not None and bias.grad is not None:
        logger.info("✅ 模型场景梯度正常")
        return True
    else:
        logger.error("❌ 模型场景梯度失败")
        return False

def check_grad_enabled_context():
    """检查梯度启用上下文"""
    logger.info("🔍 检查梯度上下文...")
    
    # 测试不同的梯度上下文
    logger.info(f"默认状态: {torch.is_grad_enabled()}")
    
    with torch.set_grad_enabled(True):
        logger.info(f"with torch.set_grad_enabled(True): {torch.is_grad_enabled()}")
        
        x = torch.tensor([1.0], requires_grad=True)
        y = x * 2
        logger.info(f"在True上下文中 y.requires_grad: {y.requires_grad}")
    
    with torch.set_grad_enabled(False):
        logger.info(f"with torch.set_grad_enabled(False): {torch.is_grad_enabled()}")
        
        x = torch.tensor([1.0], requires_grad=True)
        y = x * 2
        logger.info(f"在False上下文中 y.requires_grad: {y.requires_grad}")
    
    logger.info(f"退出上下文后: {torch.is_grad_enabled()}")

def main():
    """主函数"""
    logger.info("🚀 简单梯度测试开始...")
    
    # 确保梯度计算启用
    torch.set_grad_enabled(True)
    
    results = []
    
    # 运行各种测试
    results.append(test_basic_gradients())
    results.append(test_tensor_operations())
    results.append(test_model_like_scenario())
    
    # 检查上下文
    check_grad_enabled_context()
    
    # 总结
    logger.info("📊 测试结果总结:")
    logger.info(f"基础梯度测试: {'✅ 通过' if results[0] else '❌ 失败'}")
    logger.info(f"张量操作测试: {'✅ 通过' if results[1] else '❌ 失败'}")
    logger.info(f"模型场景测试: {'✅ 通过' if results[2] else '❌ 失败'}")
    
    if all(results):
        logger.info("🎉 所有梯度测试通过！")
        logger.info("💡 建议检查以下几点:")
        logger.info("   1. 确保在训练代码开始处调用 torch.set_grad_enabled(True)")
        logger.info("   2. 确保模型处于训练模式 model.train()")
        logger.info("   3. 确保LoRA参数的requires_grad=True")
        logger.info("   4. 检查是否有意外的torch.no_grad()或torch.set_grad_enabled(False)")
    else:
        logger.error("❌ 部分梯度测试失败，需要进一步调查")
    
    logger.info("🎯 测试完成")

if __name__ == "__main__":
    main()
