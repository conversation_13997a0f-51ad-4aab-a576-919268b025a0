#!/usr/bin/env python3
"""
运行红外目标检测评估
对比预测结果和标准结果
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InfraredEvaluator:
    """红外目标检测评估器"""
    
    def __init__(self, iou_threshold: float = 0.5):
        """
        初始化评估器
        
        Args:
            iou_threshold: IoU阈值，用于判断检测是否正确
        """
        self.iou_threshold = iou_threshold
        
        # 目标类别映射
        self.class_names = {
            0: "drone",
            1: "car", 
            2: "ship",
            3: "bus",
            4: "pedestrian",
            5: "cyclist"
        }
        
    def calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """
        计算两个边界框的IoU
        
        Args:
            box1: [x_center, y_center, width, height] (归一化坐标)
            box2: [x_center, y_center, width, height] (归一化坐标)
            
        Returns:
            IoU值
        """
        # 转换为 [x1, y1, x2, y2] 格式
        x1_1 = box1[0] - box1[2] / 2
        y1_1 = box1[1] - box1[3] / 2
        x2_1 = box1[0] + box1[2] / 2
        y2_1 = box1[1] + box1[3] / 2
        
        x1_2 = box2[0] - box2[2] / 2
        y1_2 = box2[1] - box2[3] / 2
        x2_2 = box2[0] + box2[2] / 2
        y2_2 = box2[1] + box2[3] / 2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = box1[2] * box1[3]
        area2 = box2[2] * box2[3]
        union_area = area1 + area2 - inter_area
        
        if union_area <= 0:
            return 0.0
        
        return inter_area / union_area
    
    def load_ground_truth(self, labels_dir: str, frame_paths: List[str]) -> Dict[str, List[Dict]]:
        """
        加载标准结果
        
        Args:
            labels_dir: 标注文件目录
            frame_paths: 帧文件路径列表
            
        Returns:
            标准结果字典
        """
        ground_truth = {}
        
        for frame_path in frame_paths:
            # 从路径中提取帧名称
            frame_name = Path(frame_path).stem
            
            # 构建标注文件路径
            # 从frame_path中提取data目录名
            parts = Path(frame_path).parts
            data_dir = None
            for part in parts:
                if part.startswith('data'):
                    data_dir = part
                    break
            
            if data_dir is None:
                logger.warning(f"无法从路径中提取data目录: {frame_path}")
                ground_truth[frame_name] = []
                continue
            
            label_file = Path(labels_dir) / data_dir / f"{frame_name}.txt"
            
            if not label_file.exists():
                logger.warning(f"标注文件不存在: {label_file}")
                ground_truth[frame_name] = []
                continue
            
            # 读取标注
            annotations = []
            try:
                with open(label_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            parts = line.split()
                            if len(parts) >= 5:
                                class_id = int(parts[0])
                                x_center = float(parts[1])
                                y_center = float(parts[2])
                                width = float(parts[3])
                                height = float(parts[4])
                                
                                annotations.append({
                                    'class_id': class_id,
                                    'class_name': self.class_names.get(class_id, f"class_{class_id}"),
                                    'bbox': [x_center, y_center, width, height],
                                    'confidence': 1.0  # 标准结果置信度为1
                                })
                
                ground_truth[frame_name] = annotations
                logger.debug(f"加载标注 {frame_name}: {len(annotations)} 个目标")
                
            except Exception as e:
                logger.error(f"读取标注文件失败 {label_file}: {e}")
                ground_truth[frame_name] = []
        
        return ground_truth
    
    def parse_predictions(self, predictions: Dict[str, List]) -> Dict[str, List[Dict]]:
        """
        解析预测结果
        
        Args:
            predictions: 原始预测结果
            
        Returns:
            解析后的预测结果
        """
        parsed_predictions = {}
        
        for frame_name, detections in predictions.items():
            parsed_detections = []
            
            for detection in detections:
                if isinstance(detection, dict):
                    # 假设检测结果格式为 {'class': 'drone', 'bbox': [x, y, w, h], 'confidence': 0.9}
                    class_name = detection.get('class', 'unknown')
                    bbox = detection.get('bbox', [0, 0, 0, 0])
                    confidence = detection.get('confidence', 0.0)
                    
                    # 查找类别ID
                    class_id = None
                    for cid, cname in self.class_names.items():
                        if cname == class_name:
                            class_id = cid
                            break
                    
                    if class_id is not None:
                        parsed_detections.append({
                            'class_id': class_id,
                            'class_name': class_name,
                            'bbox': bbox,
                            'confidence': confidence
                        })
            
            parsed_predictions[frame_name] = parsed_detections
        
        return parsed_predictions
    
    def evaluate_sequence(self, predictions: Dict[str, List], ground_truth: Dict[str, List]) -> Dict[str, Any]:
        """
        评估单个序列
        
        Args:
            predictions: 预测结果
            ground_truth: 标准结果
            
        Returns:
            评估指标
        """
        total_tp = 0  # True Positives
        total_fp = 0  # False Positives
        total_fn = 0  # False Negatives
        total_predictions = 0
        total_ground_truths = 0
        
        frame_results = {}
        
        for frame_name in ground_truth.keys():
            gt_boxes = ground_truth.get(frame_name, [])
            pred_boxes = predictions.get(frame_name, [])
            
            total_ground_truths += len(gt_boxes)
            total_predictions += len(pred_boxes)
            
            # 匹配预测和标准结果
            matched_gt = set()
            matched_pred = set()
            
            tp = 0
            fp = 0
            
            # 对每个预测框找最佳匹配的标准框
            for i, pred in enumerate(pred_boxes):
                best_iou = 0
                best_gt_idx = -1
                
                for j, gt in enumerate(gt_boxes):
                    if j in matched_gt:
                        continue
                    
                    # 只比较相同类别的框
                    if pred['class_id'] == gt['class_id']:
                        iou = self.calculate_iou(pred['bbox'], gt['bbox'])
                        if iou > best_iou:
                            best_iou = iou
                            best_gt_idx = j
                
                if best_iou >= self.iou_threshold and best_gt_idx != -1:
                    # True Positive
                    tp += 1
                    matched_gt.add(best_gt_idx)
                    matched_pred.add(i)
                else:
                    # False Positive
                    fp += 1
            
            # 未匹配的标准框为False Negative
            fn = len(gt_boxes) - len(matched_gt)
            
            total_tp += tp
            total_fp += fp
            total_fn += fn
            
            frame_results[frame_name] = {
                'tp': tp,
                'fp': fp,
                'fn': fn,
                'predictions': len(pred_boxes),
                'ground_truths': len(gt_boxes)
            }
        
        # 计算总体指标
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'total_tp': total_tp,
            'total_fp': total_fp,
            'total_fn': total_fn,
            'total_predictions': total_predictions,
            'total_ground_truths': total_ground_truths,
            'frame_results': frame_results
        }
    
    def run_evaluation(self, predictions_dir: str, labels_dir: str) -> Dict[str, Any]:
        """
        运行完整评估
        
        Args:
            predictions_dir: 预测结果目录
            labels_dir: 标注文件目录
            
        Returns:
            评估结果
        """
        logger.info("🚀 开始评估...")
        
        # 加载所有预测结果
        predictions_dir = Path(predictions_dir)
        all_results = {}
        sequence_results = {}
        
        # 遍历所有预测文件
        for pred_file in predictions_dir.glob("prediction_sequence_*.json"):
            logger.info(f"处理预测文件: {pred_file.name}")
            
            try:
                with open(pred_file, 'r', encoding='utf-8') as f:
                    pred_data = json.load(f)
                
                sequence_id = pred_data.get('sequence_id', 0)
                frame_paths = pred_data.get('frame_paths', [])
                detections = pred_data.get('detections', {})
                
                # 加载对应的标准结果
                ground_truth = self.load_ground_truth(labels_dir, frame_paths)
                
                # 解析预测结果
                parsed_predictions = self.parse_predictions(detections)
                
                # 评估这个序列
                seq_result = self.evaluate_sequence(parsed_predictions, ground_truth)
                seq_result['sequence_id'] = sequence_id
                seq_result['frame_count'] = len(frame_paths)
                
                sequence_results[sequence_id] = seq_result
                
                logger.info(f"序列 {sequence_id}: P={seq_result['precision']:.3f}, R={seq_result['recall']:.3f}, F1={seq_result['f1_score']:.3f}")
                
            except Exception as e:
                logger.error(f"处理预测文件失败 {pred_file}: {e}")
        
        # 计算总体指标
        total_tp = sum(r['total_tp'] for r in sequence_results.values())
        total_fp = sum(r['total_fp'] for r in sequence_results.values())
        total_fn = sum(r['total_fn'] for r in sequence_results.values())
        total_predictions = sum(r['total_predictions'] for r in sequence_results.values())
        total_ground_truths = sum(r['total_ground_truths'] for r in sequence_results.values())
        
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0
        
        all_results = {
            'overall_metrics': {
                'precision': overall_precision,
                'recall': overall_recall,
                'f1_score': overall_f1,
                'total_tp': total_tp,
                'total_fp': total_fp,
                'total_fn': total_fn,
                'total_predictions': total_predictions,
                'total_ground_truths': total_ground_truths
            },
            'sequence_results': sequence_results,
            'evaluation_config': {
                'iou_threshold': self.iou_threshold,
                'class_names': self.class_names
            }
        }
        
        return all_results

def main():
    """主函数"""
    # 配置路径
    predictions_dir = "./output/predictions"
    labels_dir = "../tiaozhanbei_datasets/labels"
    output_file = "./output/evaluation_results.json"
    
    # 检查路径
    if not Path(predictions_dir).exists():
        logger.error(f"预测结果目录不存在: {predictions_dir}")
        return
    
    if not Path(labels_dir).exists():
        logger.error(f"标注目录不存在: {labels_dir}")
        return
    
    # 创建评估器
    evaluator = InfraredEvaluator(iou_threshold=0.5)
    
    # 运行评估
    results = evaluator.run_evaluation(predictions_dir, labels_dir)
    
    # 保存结果
    os.makedirs(Path(output_file).parent, exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 打印结果
    overall = results['overall_metrics']
    logger.info("📊 评估结果:")
    logger.info(f"总体精确率 (Precision): {overall['precision']:.4f}")
    logger.info(f"总体召回率 (Recall): {overall['recall']:.4f}")
    logger.info(f"总体F1分数: {overall['f1_score']:.4f}")
    logger.info(f"总预测数: {overall['total_predictions']}")
    logger.info(f"总标准数: {overall['total_ground_truths']}")
    logger.info(f"True Positives: {overall['total_tp']}")
    logger.info(f"False Positives: {overall['total_fp']}")
    logger.info(f"False Negatives: {overall['total_fn']}")
    
    logger.info(f"详细结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
