"""
红外目标检测评估模块
参考LLaMA-Factory的evaluator.py实现，专门用于红外目标检测任务的评估
"""

import json
import os
import logging
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import torch

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class DetectionMetrics:
    """检测指标数据类"""
    precision: float
    recall: float
    f1_score: float
    ap: float  # Average Precision
    total_predictions: int
    total_ground_truths: int
    true_positives: int
    false_positives: int
    false_negatives: int

class InfraredDetectionEvaluator:
    """红外目标检测评估器"""
    
    def __init__(self, iou_threshold: float = 0.5, confidence_threshold: float = 0.5):
        """
        初始化评估器
        
        Args:
            iou_threshold: IoU阈值，用于判断检测是否正确
            confidence_threshold: 置信度阈值，用于过滤低置信度检测
        """
        self.iou_threshold = iou_threshold
        self.confidence_threshold = confidence_threshold
        
        # 目标类别
        self.classes = ["drone", "car", "ship", "bus", "pedestrian", "cyclist"]
        
    def calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """
        计算两个边界框的IoU
        
        Args:
            box1: [x1, y1, x2, y2]
            box2: [x1, y1, x2, y2]
            
        Returns:
            IoU值
        """
        # 计算交集
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        
        # 计算并集
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection
        
        if union <= 0:
            return 0.0
        
        return intersection / union
    
    def match_detections(self, predictions: List[Dict], ground_truths: List[Dict]) -> Tuple[List[bool], List[bool]]:
        """
        匹配预测和真实标注
        
        Args:
            predictions: 预测结果列表
            ground_truths: 真实标注列表
            
        Returns:
            (pred_matched, gt_matched) 匹配状态列表
        """
        pred_matched = [False] * len(predictions)
        gt_matched = [False] * len(ground_truths)
        
        # 按置信度排序预测结果
        sorted_preds = sorted(enumerate(predictions), 
                            key=lambda x: x[1].get('confidence', 0), reverse=True)
        
        for pred_idx, pred in sorted_preds:
            if pred.get('confidence', 0) < self.confidence_threshold:
                continue
                
            best_iou = 0
            best_gt_idx = -1
            
            for gt_idx, gt in enumerate(ground_truths):
                if gt_matched[gt_idx]:
                    continue
                    
                # 检查类别是否匹配
                if pred.get('class_name') != gt.get('class_name'):
                    continue
                
                # 计算IoU
                iou = self.calculate_iou(pred['bbox'], gt['bbox'])
                
                if iou > best_iou and iou >= self.iou_threshold:
                    best_iou = iou
                    best_gt_idx = gt_idx
            
            if best_gt_idx >= 0:
                pred_matched[pred_idx] = True
                gt_matched[best_gt_idx] = True
        
        return pred_matched, gt_matched
    
    def evaluate_frame(self, predictions: List[Dict], ground_truths: List[Dict]) -> Dict[str, int]:
        """
        评估单帧的检测结果
        
        Args:
            predictions: 预测结果列表
            ground_truths: 真实标注列表
            
        Returns:
            包含TP, FP, FN数量的字典
        """
        # 过滤低置信度预测
        filtered_preds = [p for p in predictions if p.get('confidence', 0) >= self.confidence_threshold]
        
        pred_matched, gt_matched = self.match_detections(filtered_preds, ground_truths)
        
        tp = sum(pred_matched)
        fp = len(filtered_preds) - tp
        fn = len(ground_truths) - sum(gt_matched)
        
        return {
            'tp': tp,
            'fp': fp,
            'fn': fn,
            'total_predictions': len(filtered_preds),
            'total_ground_truths': len(ground_truths)
        }
    
    def evaluate_sequence(self, prediction_file: str, ground_truth_file: str) -> DetectionMetrics:
        """
        评估整个序列的检测结果
        
        Args:
            prediction_file: 预测结果文件路径
            ground_truth_file: 真实标注文件路径
            
        Returns:
            检测指标
        """
        # 加载预测结果
        with open(prediction_file, 'r', encoding='utf-8') as f:
            predictions = json.load(f)
        
        # 加载真实标注
        with open(ground_truth_file, 'r', encoding='utf-8') as f:
            ground_truths = json.load(f)
        
        total_tp = 0
        total_fp = 0
        total_fn = 0
        total_predictions = 0
        total_ground_truths = 0
        
        # 遍历所有帧
        pred_detections = predictions.get('detections', {})
        gt_detections = ground_truths.get('detections', {})
        
        all_frames = set(pred_detections.keys()) | set(gt_detections.keys())
        
        for frame_id in all_frames:
            frame_preds = pred_detections.get(frame_id, [])
            frame_gts = gt_detections.get(frame_id, [])
            
            frame_metrics = self.evaluate_frame(frame_preds, frame_gts)
            
            total_tp += frame_metrics['tp']
            total_fp += frame_metrics['fp']
            total_fn += frame_metrics['fn']
            total_predictions += frame_metrics['total_predictions']
            total_ground_truths += frame_metrics['total_ground_truths']
        
        # 计算指标
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        # 简化的AP计算（这里使用F1分数作为近似）
        ap = f1_score
        
        return DetectionMetrics(
            precision=precision,
            recall=recall,
            f1_score=f1_score,
            ap=ap,
            total_predictions=total_predictions,
            total_ground_truths=total_ground_truths,
            true_positives=total_tp,
            false_positives=total_fp,
            false_negatives=total_fn
        )
    
    def evaluate_dataset(self, predictions_dir: str, ground_truths_dir: str) -> Dict[str, Any]:
        """
        评估整个数据集
        
        Args:
            predictions_dir: 预测结果目录
            ground_truths_dir: 真实标注目录
            
        Returns:
            评估结果字典
        """
        predictions_dir = Path(predictions_dir)
        ground_truths_dir = Path(ground_truths_dir)
        
        all_metrics = []
        sequence_results = {}
        
        # 遍历所有预测文件
        for pred_file in predictions_dir.glob("prediction_sequence_*.json"):
            sequence_id = pred_file.stem.split('_')[-1]
            gt_file = ground_truths_dir / f"ground_truth_sequence_{sequence_id}.json"
            
            if not gt_file.exists():
                logger.warning(f"找不到对应的真实标注文件: {gt_file}")
                continue
            
            try:
                metrics = self.evaluate_sequence(str(pred_file), str(gt_file))
                all_metrics.append(metrics)
                sequence_results[sequence_id] = {
                    'precision': metrics.precision,
                    'recall': metrics.recall,
                    'f1_score': metrics.f1_score,
                    'ap': metrics.ap
                }
                
                logger.info(f"序列 {sequence_id}: P={metrics.precision:.3f}, R={metrics.recall:.3f}, F1={metrics.f1_score:.3f}")
                
            except Exception as e:
                logger.error(f"评估序列 {sequence_id} 失败: {e}")
                continue
        
        if not all_metrics:
            logger.error("没有成功评估的序列")
            return {}
        
        # 计算平均指标
        avg_precision = np.mean([m.precision for m in all_metrics])
        avg_recall = np.mean([m.recall for m in all_metrics])
        avg_f1 = np.mean([m.f1_score for m in all_metrics])
        avg_ap = np.mean([m.ap for m in all_metrics])
        
        total_tp = sum([m.true_positives for m in all_metrics])
        total_fp = sum([m.false_positives for m in all_metrics])
        total_fn = sum([m.false_negatives for m in all_metrics])
        
        # 整体指标
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0
        
        results = {
            'average_metrics': {
                'precision': avg_precision,
                'recall': avg_recall,
                'f1_score': avg_f1,
                'ap': avg_ap
            },
            'overall_metrics': {
                'precision': overall_precision,
                'recall': overall_recall,
                'f1_score': overall_f1,
                'total_tp': total_tp,
                'total_fp': total_fp,
                'total_fn': total_fn
            },
            'sequence_results': sequence_results,
            'evaluation_config': {
                'iou_threshold': self.iou_threshold,
                'confidence_threshold': self.confidence_threshold,
                'num_sequences': len(all_metrics)
            }
        }
        
        return results

def main():
    """主函数"""
    # 配置路径
    predictions_dir = "output/predictions"
    ground_truths_dir = "data/ground_truths"  # 需要准备真实标注数据
    output_file = "output/evaluation_results.json"
    
    # 创建评估器
    evaluator = InfraredDetectionEvaluator(iou_threshold=0.5, confidence_threshold=0.3)
    
    try:
        # 评估数据集
        logger.info("开始评估...")
        results = evaluator.evaluate_dataset(predictions_dir, ground_truths_dir)
        
        if results:
            # 保存结果
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            # 打印结果
            logger.info("评估完成!")
            logger.info(f"平均精确度: {results['average_metrics']['precision']:.3f}")
            logger.info(f"平均召回率: {results['average_metrics']['recall']:.3f}")
            logger.info(f"平均F1分数: {results['average_metrics']['f1_score']:.3f}")
            logger.info(f"整体精确度: {results['overall_metrics']['precision']:.3f}")
            logger.info(f"整体召回率: {results['overall_metrics']['recall']:.3f}")
            logger.info(f"整体F1分数: {results['overall_metrics']['f1_score']:.3f}")
            logger.info(f"结果已保存到: {output_file}")
        else:
            logger.error("评估失败")
            
    except Exception as e:
        logger.error(f"评估过程出错: {e}")
        raise

if __name__ == "__main__":
    main()
