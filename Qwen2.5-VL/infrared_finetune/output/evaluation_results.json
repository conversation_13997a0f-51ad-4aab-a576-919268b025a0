{"overall_metrics": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 75, "total_predictions": 0, "total_ground_truths": 75}, "sequence_results": {"9": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"29": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "34": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "39": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "44": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "49": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 9, "frame_count": 5}, "10": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"50": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "55": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "60": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "65": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "70": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 10, "frame_count": 5}, "13": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"53": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "58": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "63": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "68": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "73": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 13, "frame_count": 5}, "8": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"28": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "33": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "38": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "43": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "48": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 8, "frame_count": 5}, "3": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"3": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "8": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "13": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "18": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "23": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 3, "frame_count": 5}, "0": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"0": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "5": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "10": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "15": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "20": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 0, "frame_count": 5}, "14": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"54": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "59": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "64": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "69": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "74": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 14, "frame_count": 5}, "12": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"52": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "57": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "62": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "67": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "72": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 12, "frame_count": 5}, "4": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"4": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "9": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "14": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "19": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "24": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 4, "frame_count": 5}, "7": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"27": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "32": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "37": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "42": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "47": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 7, "frame_count": 5}, "5": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"25": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "30": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "35": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "40": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "45": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 5, "frame_count": 5}, "2": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"2": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "7": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "12": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "17": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "22": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 2, "frame_count": 5}, "6": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"26": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "31": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "36": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "41": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "46": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 6, "frame_count": 5}, "11": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"51": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "56": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "61": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "66": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "71": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 11, "frame_count": 5}, "1": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "total_tp": 0, "total_fp": 0, "total_fn": 5, "total_predictions": 0, "total_ground_truths": 5, "frame_results": {"1": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "6": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "11": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "16": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}, "21": {"tp": 0, "fp": 0, "fn": 1, "predictions": 0, "ground_truths": 1}}, "sequence_id": 1, "frame_count": 5}}, "evaluation_config": {"iou_threshold": 0.5, "class_names": {"0": "drone", "1": "car", "2": "ship", "3": "bus", "4": "pedestrian", "5": "cyclist"}}}