{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 2.9523809523809526, "eval_steps": 500, "global_step": 156, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09523809523809523, "grad_norm": 15.936338424682617, "learning_rate": 2.5e-06, "loss": 12.2325, "step": 5}, {"epoch": 0.19047619047619047, "grad_norm": 40.69731903076172, "learning_rate": 5.625e-06, "loss": 11.2179, "step": 10}, {"epoch": 0.2857142857142857, "grad_norm": 38.34971237182617, "learning_rate": 8.750000000000001e-06, "loss": 11.687, "step": 15}, {"epoch": 0.38095238095238093, "grad_norm": 27.74287223815918, "learning_rate": 9.98867437523228e-06, "loss": 11.1935, "step": 20}, {"epoch": 0.47619047619047616, "grad_norm": 12.88186264038086, "learning_rate": 9.91964794299315e-06, "loss": 12.2804, "step": 25}, {"epoch": 0.5714285714285714, "grad_norm": 48.756019592285156, "learning_rate": 9.788754083424654e-06, "loss": 11.1572, "step": 30}, {"epoch": 0.6666666666666666, "grad_norm": 45.27677536010742, "learning_rate": 9.597638862757255e-06, "loss": 11.0414, "step": 35}, {"epoch": 0.7619047619047619, "grad_norm": 9.435616493225098, "learning_rate": 9.348705665778479e-06, "loss": 8.8311, "step": 40}, {"epoch": 0.8571428571428571, "grad_norm": 12.145771980285645, "learning_rate": 9.045084971874738e-06, "loss": 10.3217, "step": 45}, {"epoch": 0.9523809523809523, "grad_norm": 74.89927673339844, "learning_rate": 8.690594987436705e-06, "loss": 8.217, "step": 50}, {"epoch": 1.0380952380952382, "grad_norm": 21.96805191040039, "learning_rate": 8.289693629698564e-06, "loss": 8.19, "step": 55}, {"epoch": 1.1333333333333333, "grad_norm": 30.657976150512695, "learning_rate": 7.84742246584226e-06, "loss": 7.9333, "step": 60}, {"epoch": 1.2285714285714286, "grad_norm": 14.133464813232422, "learning_rate": 7.369343312364994e-06, "loss": 8.1596, "step": 65}, {"epoch": 1.3238095238095238, "grad_norm": 18.220802307128906, "learning_rate": 6.8614682920097265e-06, "loss": 5.8149, "step": 70}, {"epoch": 1.4190476190476191, "grad_norm": 29.594526290893555, "learning_rate": 6.330184227833376e-06, "loss": 7.0239, "step": 75}, {"epoch": 1.5142857142857142, "grad_norm": 9.254288673400879, "learning_rate": 5.782172325201155e-06, "loss": 6.7143, "step": 80}, {"epoch": 1.6095238095238096, "grad_norm": 3.774155378341675, "learning_rate": 5.224324151752575e-06, "loss": 6.6234, "step": 85}, {"epoch": 1.704761904761905, "grad_norm": 4.135085582733154, "learning_rate": 4.663654971939802e-06, "loss": 5.6883, "step": 90}, {"epoch": 1.8, "grad_norm": 2.7360353469848633, "learning_rate": 4.107215526006818e-06, "loss": 6.3207, "step": 95}, {"epoch": 1.8952380952380952, "grad_norm": 2.4382591247558594, "learning_rate": 3.562003362839914e-06, "loss": 5.8874, "step": 100}, {"epoch": 1.9904761904761905, "grad_norm": 1.811694622039795, "learning_rate": 3.0348748417303826e-06, "loss": 5.4397, "step": 105}, {"epoch": 2.0761904761904764, "grad_norm": 2.0508198738098145, "learning_rate": 2.532458909678266e-06, "loss": 5.6763, "step": 110}, {"epoch": 2.1714285714285713, "grad_norm": 2.090500593185425, "learning_rate": 2.061073738537635e-06, "loss": 5.4924, "step": 115}, {"epoch": 2.2666666666666666, "grad_norm": 2.2942497730255127, "learning_rate": 1.6266472703396286e-06, "loss": 6.0983, "step": 120}, {"epoch": 2.361904761904762, "grad_norm": 1.4562374353408813, "learning_rate": 1.234642669981946e-06, "loss": 5.2173, "step": 125}, {"epoch": 2.4571428571428573, "grad_norm": 5.402444362640381, "learning_rate": 8.899896227604509e-07, "loss": 5.0313, "step": 130}, {"epoch": 2.552380952380952, "grad_norm": 1.9393961429595947, "learning_rate": 5.9702234071631e-07, "loss": 5.6075, "step": 135}, {"epoch": 2.6476190476190475, "grad_norm": 1.0366257429122925, "learning_rate": 3.5942505740480583e-07, "loss": 5.3893, "step": 140}, {"epoch": 2.742857142857143, "grad_norm": 1.3899941444396973, "learning_rate": 1.801856965207338e-07, "loss": 5.1465, "step": 145}, {"epoch": 2.8380952380952382, "grad_norm": 1.0808625221252441, "learning_rate": 6.15582970243117e-08, "loss": 5.3136, "step": 150}, {"epoch": 2.9333333333333336, "grad_norm": 2.232487201690674, "learning_rate": 5.034667293427053e-09, "loss": 5.8103, "step": 155}], "logging_steps": 5, "max_steps": 156, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 3.85837406142935e+16, "train_batch_size": 1, "trial_name": null, "trial_params": null}