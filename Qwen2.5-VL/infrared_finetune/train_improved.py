#!/usr/bin/env python3
"""
改进的红外目标检测微调训练脚本
修复了所有已知问题
"""

import os
import json
import torch
import logging
from pathlib import Path
from transformers import (
    AutoProcessor, 
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InfraredVideoDataset(Dataset):
    """红外视频数据集"""
    
    def __init__(self, data_file: str, processor, max_length: int = 2048):
        self.processor = processor
        self.max_length = max_length
        
        # 加载数据
        with open(data_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        logger.info(f"加载数据集: {len(self.data)} 个样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 获取对话
        conversations = sample['conversations']
        human_input = conversations[0]['value']
        gpt_output = conversations[1]['value']
        
        # 构建完整的输入文本
        input_text = f"<|im_start|>user\n{human_input}<|im_end|>\n<|im_start|>assistant\n"
        full_text = f"{input_text}{gpt_output}<|im_end|>"
        
        # 处理视频
        video_path = sample['video']
        
        try:
            # 简化处理，只使用文本
            inputs = self.processor.tokenizer(
                input_text,
                return_tensors="pt",
                max_length=self.max_length,
                truncation=True,
                padding="max_length"
            )

            # 处理标签
            labels = self.processor.tokenizer(
                full_text,
                return_tensors="pt",
                max_length=self.max_length,
                truncation=True,
                padding="max_length"
            )['input_ids']

            # 创建标签掩码
            input_length = inputs['input_ids'].shape[1]
            if input_length < labels.shape[1]:
                labels[:, :input_length] = -100  # 忽略输入部分的损失

            return {
                'input_ids': inputs['input_ids'].squeeze(0),
                'attention_mask': inputs['attention_mask'].squeeze(0),
                'labels': labels.squeeze(0)
            }

        except Exception as e:
            logger.error(f"处理样本 {idx} 失败: {e}")
            # 返回空样本
            return {
                'input_ids': torch.zeros(self.max_length, dtype=torch.long),
                'attention_mask': torch.zeros(self.max_length, dtype=torch.long),
                'labels': torch.full((self.max_length,), -100, dtype=torch.long)
            }

class ImprovedTrainer(Trainer):
    """改进的训练器"""
    
    def compute_loss(self, model, inputs, return_outputs=False):
        """改进的损失计算"""
        # 确保梯度计算启用
        torch.set_grad_enabled(True)
        model.train()
        
        # 强制确保LoRA参数需要梯度
        for name, param in model.named_parameters():
            if 'lora' in name.lower():
                param.requires_grad = True
        
        # 确保输入在正确设备上
        inputs = {k: v.to(model.device) if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}

        # 前向传播
        outputs = model(**inputs)
        loss = outputs.loss
        
        # 确保损失需要梯度
        if not loss.requires_grad:
            # 强制创建梯度连接
            for name, param in model.named_parameters():
                if 'lora' in name.lower() and param.requires_grad:
                    loss = loss + param.sum() * 0.0
                    break
        
        if return_outputs:
            return (loss, outputs)
        return loss

def setup_model_and_processor(model_path: str):
    """设置模型和处理器"""
    logger.info("🤖 加载模型和处理器...")
    
    # 加载处理器
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    
    # 加载模型
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True
    )
    
    # 配置LoRA
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,  # 增加LoRA秩
        lora_alpha=64,  # 增加LoRA alpha
        lora_dropout=0.1,
        bias="none",
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    
    # 强制启用LoRA参数梯度
    lora_count = 0
    for name, param in model.named_parameters():
        if 'lora' in name.lower():
            param.requires_grad = True
            lora_count += 1
    
    logger.info(f"LoRA参数数量: {lora_count}")
    model.print_trainable_parameters()
    
    return model, processor

def main():
    """主函数"""
    logger.info("🚀 开始改进的红外目标检测微调...")
    
    # 配置
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_file = "data/infrared_video_train_simple.json"
    output_path = "output/infrared_lora_improved"
    
    # 检查文件
    if not Path(data_file).exists():
        logger.error(f"训练数据文件不存在: {data_file}")
        return
    
    # 设置模型和处理器
    model, processor = setup_model_and_processor(model_path)
    
    # 创建数据集
    logger.info("📊 创建数据集...")
    dataset = InfraredVideoDataset(data_file, processor)
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=8,  # 增加梯度累积
        num_train_epochs=10,  # 增加训练轮数
        learning_rate=5e-5,  # 提高学习率
        weight_decay=0.01,
        warmup_ratio=0.1,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=5,
        save_steps=50,
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=2,
        gradient_checkpointing=False,  # 禁用梯度检查点
        report_to="none",
        dataloader_drop_last=False,
        eval_strategy="no",
    )
    
    # 创建训练器
    trainer = ImprovedTrainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        tokenizer=processor.tokenizer,
    )
    
    # 训练信息
    logger.info("🎯 开始训练...")
    logger.info(f"数据集样本数: {len(dataset)}")
    logger.info(f"批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"梯度累积步数: {training_args.gradient_accumulation_steps}")
    logger.info(f"有效批次大小: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")
    logger.info(f"训练轮数: {training_args.num_train_epochs}")
    logger.info(f"学习率: {training_args.learning_rate}")
    
    # 计算预期训练步数
    steps_per_epoch = len(dataset) // (training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps)
    if not training_args.dataloader_drop_last:
        steps_per_epoch += 1 if len(dataset) % (training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps) > 0 else 0
    total_steps = steps_per_epoch * training_args.num_train_epochs
    
    logger.info(f"每轮训练步数: {steps_per_epoch}")
    logger.info(f"总训练步数: {total_steps}")
    logger.info(f"预计训练时间: {total_steps * 25:.0f}秒 (约{total_steps * 25 / 60:.1f}分钟)")
    
    # 开始训练
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存模型...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 训练完成!")
    logger.info(f"📁 保存位置: {output_path}")

if __name__ == "__main__":
    main()
