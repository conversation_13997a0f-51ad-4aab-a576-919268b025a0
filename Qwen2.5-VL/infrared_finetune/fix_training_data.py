#!/usr/bin/env python3
"""
修复训练数据格式问题
1. 统一类别名称为drone
2. 统一坐标格式
3. 确保数据质量
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, List, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_yolo_annotations(labels_dir: str, image_path: str) -> List[Dict]:
    """
    加载YOLO格式的标注
    
    Args:
        labels_dir: 标注目录
        image_path: 图像路径
        
    Returns:
        标注列表
    """
    # 从图像路径提取信息
    path_parts = Path(image_path).parts
    
    # 找到data目录
    data_dir = None
    for part in path_parts:
        if part.startswith('data'):
            data_dir = part
            break
    
    if not data_dir:
        return []
    
    # 构建标注文件路径
    frame_name = Path(image_path).stem
    label_file = Path(labels_dir) / data_dir / f"{frame_name}.txt"
    
    if not label_file.exists():
        return []
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        
                        # 转换为像素坐标 (假设图像尺寸为256x256)
                        img_w, img_h = 256, 256
                        x_center_px = x_center * img_w
                        y_center_px = y_center * img_h
                        width_px = width * img_w
                        height_px = height * img_h
                        
                        x1 = int(x_center_px - width_px / 2)
                        y1 = int(y_center_px - height_px / 2)
                        x2 = int(x_center_px + width_px / 2)
                        y2 = int(y_center_px + height_px / 2)
                        
                        annotations.append({
                            'bbox': [x1, y1, x2, y2],
                            'class_name': 'drone',  # 统一为drone类别
                            'confidence': 1.0
                        })
    except Exception as e:
        logger.error(f"读取标注文件失败 {label_file}: {e}")
    
    return annotations

def create_improved_prompt():
    """创建改进的提示词"""
    return """<video>
这是一个红外视频序列，包含连续的5帧图像。请仔细分析这个视频中的无人机目标。

任务要求：
1. 检测每帧中的无人机目标（通常表现为小的亮点）
2. 利用视频的时序信息确认目标的真实性
3. 观察目标在不同帧中的位置变化
4. 精确定位目标的边界框

检测重点：
- 无人机目标通常是小的白色或亮色斑点
- 目标会在帧间发生位置变化
- 排除静态的背景噪声
- 提供精确的边界框坐标

请以JSON格式输出每一帧的检测结果，帧编号对应实际的帧名称：
```json
{
  "frame_name_1": [
    {
      "bbox": [x1, y1, x2, y2],
      "class_name": "drone",
      "confidence": 0.95
    }
  ],
  "frame_name_2": [...]
}
```

注意：
- bbox格式为[x1, y1, x2, y2]，使用像素坐标
- class_name统一使用"drone"
- confidence表示检测置信度
- 如果某帧没有目标，返回空列表[]"""

def fix_training_data(input_file: str, output_file: str, labels_dir: str):
    """
    修复训练数据
    
    Args:
        input_file: 原始训练数据文件
        output_file: 修复后的训练数据文件
        labels_dir: 标注目录
    """
    logger.info("🔧 开始修复训练数据...")
    
    # 加载原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"原始数据样本数: {len(data)}")
    
    fixed_data = []
    improved_prompt = create_improved_prompt()
    
    for i, sample in enumerate(data):
        try:
            video_path = sample.get('video', '')
            if not video_path:
                continue
            
            # 构建视频帧路径
            video_dir = Path(video_path).parent
            frame_files = []
            
            # 假设视频帧按照固定间隔命名
            base_name = Path(video_path).stem
            
            # 根据原始数据推断帧名称模式
            conversations = sample.get('conversations', [])
            if len(conversations) >= 2:
                gpt_response = conversations[1].get('value', '')
                
                # 从响应中提取帧名称
                try:
                    response_json = json.loads(gpt_response)
                    frame_names = list(response_json.keys())
                    
                    # 构建完整的帧路径
                    frame_paths = []
                    for frame_name in frame_names:
                        frame_path = str(video_dir / f"{frame_name}.bmp")
                        frame_paths.append(frame_path)
                    
                    # 重新生成标准答案
                    new_response = {}
                    for frame_name, frame_path in zip(frame_names, frame_paths):
                        annotations = load_yolo_annotations(labels_dir, frame_path)
                        new_response[frame_name] = annotations
                    
                    # 创建新的训练样本
                    new_sample = {
                        'conversations': [
                            {
                                'from': 'human',
                                'value': improved_prompt
                            },
                            {
                                'from': 'gpt',
                                'value': json.dumps(new_response, ensure_ascii=False)
                            }
                        ],
                        'video': video_path
                    }
                    
                    fixed_data.append(new_sample)
                    
                    if (i + 1) % 50 == 0:
                        logger.info(f"已处理 {i + 1}/{len(data)} 个样本")
                        
                except Exception as e:
                    logger.warning(f"处理样本 {i} 失败: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"处理样本 {i} 时出错: {e}")
            continue
    
    logger.info(f"修复后数据样本数: {len(fixed_data)}")
    
    # 保存修复后的数据
    os.makedirs(Path(output_file).parent, exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(fixed_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ 修复后的训练数据已保存到: {output_file}")
    
    # 显示样本
    if fixed_data:
        logger.info("📋 修复后的样本示例:")
        sample = fixed_data[0]
        logger.info(f"视频路径: {sample['video']}")
        
        gpt_response = sample['conversations'][1]['value']
        try:
            response_data = json.loads(gpt_response)
            logger.info("标准答案示例:")
            for frame_name, detections in list(response_data.items())[:2]:
                logger.info(f"  {frame_name}: {len(detections)} 个目标")
                if detections:
                    logger.info(f"    示例: {detections[0]}")
        except:
            pass

def main():
    """主函数"""
    input_file = "data/infrared_video_train.json"
    output_file = "data/infrared_video_train_fixed.json"
    labels_dir = "../tiaozhanbei_datasets/labels"
    
    # 检查文件存在性
    if not Path(input_file).exists():
        logger.error(f"输入文件不存在: {input_file}")
        return
    
    if not Path(labels_dir).exists():
        logger.error(f"标注目录不存在: {labels_dir}")
        return
    
    # 修复训练数据
    fix_training_data(input_file, output_file, labels_dir)

if __name__ == "__main__":
    main()
