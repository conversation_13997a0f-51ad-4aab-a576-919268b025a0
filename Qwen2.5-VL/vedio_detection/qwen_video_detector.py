"""
Qwen2.5-VL视频推理模块
基于官方文档实现视频序列推理功能
"""
import os
import torch
import logging
from typing import List, Dict, Any, Optional
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

from config import MODEL_PATH, DEVICE, TORCH_DTYPE, MIN_PIXELS, MAX_PIXELS, FPS

class QwenVideoDetector:
    """Qwen2.5-VL视频检测器"""
    
    def __init__(self, model_path: str = MODEL_PATH, device: str = DEVICE, logger: logging.Logger = None):
        """
        初始化视频检测器
        
        Args:
            model_path: 模型路径
            device: 设备类型
            logger: 日志记录器
        """
        self.model_path = model_path
        self.device = device
        self.logger = logger or logging.getLogger(__name__)
        
        self.model = None
        self.processor = None
        
        self._load_model()
    
    def _load_model(self):
        """加载模型和处理器"""
        try:
            self.logger.info(f"开始加载Qwen2.5-VL模型: {self.model_path}")
            
            # 检查模型路径
            if not os.path.exists(self.model_path):
                raise ValueError(f"模型路径不存在: {self.model_path}")
            
            # 加载模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=TORCH_DTYPE,
                device_map={"": self.device},  # 指定使用特定GPU
                trust_remote_code=True
            )
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                min_pixels=MIN_PIXELS,
                max_pixels=MAX_PIXELS
            )
            
            self.logger.info("模型加载完成")
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    def detect_video_sequence(self, frame_paths: List[str], prompt: str, 
                            max_new_tokens: int = 1024) -> str:
        """
        对视频序列进行检测推理
        
        Args:
            frame_paths: 帧文件路径列表
            prompt: 检测提示词
            max_new_tokens: 最大生成token数
            
        Returns:
            模型输出文本
        """
        try:
            self.logger.info(f"开始检测视频序列，共 {len(frame_paths)} 帧")
            
            # 构造消息格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video",
                            "video": frame_paths,
                        },
                        {"type": "text", "text": prompt},
                    ],
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            self.logger.info("处理视觉信息...")
            
            # 处理视觉信息
            image_inputs, video_inputs, video_kwargs = process_vision_info(
                messages, return_video_kwargs=True
            )
            
            # 准备输入 - 移除fps参数避免冲突
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
                **video_kwargs,
            )
            
            # 移动到设备
            inputs = inputs.to(self.model.device)
            
            self.logger.info("开始模型推理...")
            
            # 推理
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs, 
                    max_new_tokens=max_new_tokens,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码输出
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, 
                skip_special_tokens=True, 
                clean_up_tokenization_spaces=False
            )
            
            result = output_text[0] if output_text else ""
            
            self.logger.info(f"推理完成，输出长度: {len(result)}")
            self.logger.debug(f"模型输出: {result[:500]}...")
            
            return result
            
        except Exception as e:
            self.logger.error(f"视频序列检测失败: {e}")
            return ""
    
    def detect_single_frame(self, frame_path: str, prompt: str, 
                          max_new_tokens: int = 512) -> str:
        """
        检测单帧图像
        
        Args:
            frame_path: 帧文件路径
            prompt: 检测提示词
            max_new_tokens: 最大生成token数
            
        Returns:
            模型输出文本
        """
        try:
            self.logger.info(f"检测单帧图像: {os.path.basename(frame_path)}")
            
            # 构造消息格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": frame_path,
                        },
                        {"type": "text", "text": prompt},
                    ],
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理视觉信息
            image_inputs, video_inputs = process_vision_info(messages)
            
            # 准备输入
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            
            # 移动到设备
            inputs = inputs.to(self.model.device)
            
            # 推理
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs, 
                    max_new_tokens=max_new_tokens,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码输出
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, 
                skip_special_tokens=True, 
                clean_up_tokenization_spaces=False
            )
            
            result = output_text[0] if output_text else ""
            
            self.logger.info(f"单帧检测完成")
            self.logger.debug(f"模型输出: {result[:200]}...")
            
            return result
            
        except Exception as e:
            self.logger.error(f"单帧检测失败: {e}")
            return ""
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if self.model is None:
            return {}
        
        return {
            'model_path': self.model_path,
            'device': str(self.model.device),
            'dtype': str(self.model.dtype),
            'num_parameters': sum(p.numel() for p in self.model.parameters()),
            'trainable_parameters': sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            if torch.cuda.is_available() and torch.cuda.device_count() > 0:
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
        except Exception as e:
            self.logger.warning(f"CUDA清理失败: {e}")

        self.logger.info("资源清理完成")
