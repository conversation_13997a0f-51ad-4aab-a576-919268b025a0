#!/usr/bin/env python3
"""
红外图像微小目标检测主程序
整合检测和可视化功能
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from detection import InfraredTargetDetector
from visualization import DetectionVisualizer

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('detection.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='红外图像微小目标检测系统')
    
    parser.add_argument(
        '--data_dir', 
        type=str, 
        default='../tiaozhanbei_datasets',
        help='数据集目录路径'
    )
    
    parser.add_argument(
        '--sequence', 
        type=str, 
        default='data04',
        help='要处理的序列名称'
    )
    
    parser.add_argument(
        '--model_path', 
        type=str, 
        default='../Qwen2.5-VL-7B-Instruct',
        help='Qwen2.5-VL模型路径'
    )
    
    parser.add_argument(
        '--output_dir', 
        type=str, 
        default='./output',
        help='输出目录'
    )
    
    parser.add_argument(
        '--frame_interval', 
        type=int, 
        default=5,
        help='帧间隔'
    )
    
    parser.add_argument(
        '--sequence_length', 
        type=int, 
        default=5,
        help='每个序列的帧数'
    )
    
    parser.add_argument(
        '--skip_detection', 
        action='store_true',
        help='跳过检测，仅进行可视化'
    )
    
    parser.add_argument(
        '--skip_visualization', 
        action='store_true',
        help='跳过可视化，仅进行检测'
    )
    
    parser.add_argument(
        '--max_sequences',
        type=int,
        default=None,
        help='最大处理序列数量（用于测试）'
    )

    parser.add_argument(
        '--frame_percentage',
        type=float,
        default=0.2,
        help='处理帧的百分比（0.0-1.0，默认0.2即20%）'
    )

    return parser.parse_args()

def run_detection(args):
    """运行检测"""
    try:
        logger.info("=" * 50)
        logger.info("开始目标检测")
        logger.info("=" * 50)
        
        # 初始化检测器
        logger.info("初始化检测器...")
        detector = InfraredTargetDetector(model_path=args.model_path)
        
        # 获取帧序列
        logger.info("获取帧序列...")
        sequences = detector.get_frame_sequence(
            args.data_dir,
            args.sequence,
            frame_interval=args.frame_interval,
            sequence_length=args.sequence_length,
            frame_percentage=args.frame_percentage
        )
        
        # 限制处理序列数量（用于测试）
        if args.max_sequences:
            sequences = sequences[:args.max_sequences]
            logger.info(f"限制处理序列数量为: {args.max_sequences}")
        
        # 创建输出目录
        os.makedirs(args.output_dir, exist_ok=True)
        
        # 处理每个序列
        all_results = []
        success_count = 0
        
        for i, frame_paths in enumerate(sequences):
            logger.info(f"处理序列 {i+1}/{len(sequences)}")
            logger.info(f"帧文件: {[Path(p).name for p in frame_paths]}")
            
            try:
                # 检测
                result = detector.detect_sequence(frame_paths)
                result["sequence_id"] = i
                all_results.append(result)
                
                # 保存单个序列结果
                sequence_output_file = os.path.join(
                    args.output_dir, 
                    f"detection_sequence_{i:03d}.json"
                )
                
                import json
                with open(sequence_output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                
                logger.info(f"序列 {i+1} 检测完成，结果已保存到 {sequence_output_file}")
                success_count += 1
                
            except Exception as e:
                logger.error(f"序列 {i+1} 检测失败: {e}")
                continue
        
        # 保存所有结果
        if all_results:
            all_results_file = os.path.join(args.output_dir, "all_detection_results.json")
            import json
            with open(all_results_file, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"所有检测完成: {success_count}/{len(sequences)} 个序列成功")
            logger.info(f"结果已保存到 {all_results_file}")
        else:
            logger.warning("没有成功的检测结果")
        
        return success_count > 0
        
    except Exception as e:
        logger.error(f"检测过程出错: {e}")
        return False

def run_visualization(args):
    """运行可视化"""
    try:
        logger.info("=" * 50)
        logger.info("开始结果可视化")
        logger.info("=" * 50)
        
        # 初始化可视化器
        logger.info("初始化可视化器...")
        visualizer = DetectionVisualizer()
        
        # 设置可视化输出目录
        visualization_output_dir = os.path.join(args.output_dir, "visualizations")
        
        # 可视化所有结果
        logger.info("开始可视化检测结果...")
        visualizer.visualize_all_results(args.output_dir, visualization_output_dir)
        
        logger.info("可视化完成！")
        return True
        
    except Exception as e:
        logger.error(f"可视化过程出错: {e}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    logger.info("红外图像微小目标检测系统启动")
    logger.info(f"参数配置:")
    logger.info(f"  数据目录: {args.data_dir}")
    logger.info(f"  序列名称: {args.sequence}")
    logger.info(f"  模型路径: {args.model_path}")
    logger.info(f"  输出目录: {args.output_dir}")
    logger.info(f"  帧间隔: {args.frame_interval}")
    logger.info(f"  序列长度: {args.sequence_length}")
    
    success = True
    
    # 运行检测
    if not args.skip_detection:
        detection_success = run_detection(args)
        if not detection_success:
            logger.error("检测失败")
            success = False
    else:
        logger.info("跳过检测步骤")
    
    # 运行可视化
    if not args.skip_visualization:
        visualization_success = run_visualization(args)
        if not visualization_success:
            logger.error("可视化失败")
            success = False
    else:
        logger.info("跳过可视化步骤")
    
    if success:
        logger.info("=" * 50)
        logger.info("系统运行完成！")
        logger.info(f"结果保存在: {args.output_dir}")
        logger.info("=" * 50)
    else:
        logger.error("系统运行过程中出现错误")
        sys.exit(1)

if __name__ == "__main__":
    main()
