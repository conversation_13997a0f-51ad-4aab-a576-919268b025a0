"""
红外图像高帧频微小目标检测主程序
使用Qwen2.5-VL模型进行视频序列目标检测
"""
import os
import sys
import argparse
import time
from datetime import datetime

# 设置使用2号GPU
os.environ["CUDA_VISIBLE_DEVICES"] = "2"

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_processor import DataProcessor
from qwen_video_detector import QwenVideoDetector
from target_detector import TargetDetector
from visualizer import DetectionVisualizer
from utils import setup_logging, create_output_directories, save_detection_results
from config import (
    DATASET_PATH, SEQUENCE_NAME, DETECTION_PROMPT, OUTPUT_DIR,
    DETECTION_OUTPUT_DIR, VISUALIZATION_OUTPUT_DIR, LOG_OUTPUT_DIR
)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="红外图像高帧频微小目标检测")
    parser.add_argument("--sequence_path", type=str, 
                       default=os.path.join(DATASET_PATH, "images", SEQUENCE_NAME),
                       help="序列图像目录路径")
    parser.add_argument("--output_dir", type=str, default=OUTPUT_DIR,
                       help="输出目录")
    parser.add_argument("--model_path", type=str, default="../Qwen2.5-VL-7B-Instruct",
                       help="模型路径")
    parser.add_argument("--max_tokens", type=int, default=1024,
                       help="最大生成token数")
    parser.add_argument("--save_individual", action="store_true", default=True,
                       help="保存单独的帧可视化")
    parser.add_argument("--save_summary", action="store_true", default=True,
                       help="保存汇总可视化")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dirs = create_output_directories(args.output_dir)
    
    # 设置日志
    logger = setup_logging(output_dirs['logs'])
    logger.info("=" * 60)
    logger.info("开始红外图像高帧频微小目标检测")
    logger.info("=" * 60)
    
    try:
        # 1. 初始化数据处理器
        logger.info("步骤1: 初始化数据处理器")
        data_processor = DataProcessor(args.sequence_path, logger)
        
        # 获取序列摘要
        sequence_summary = data_processor.get_sequence_summary()
        logger.info(f"序列摘要: {sequence_summary}")
        
        # 2. 获取data04特定序列
        logger.info("步骤2: 获取data04序列帧")
        frame_paths = data_processor.get_data04_sequence()
        
        if not frame_paths:
            logger.error("未找到有效的帧序列")
            return
        
        logger.info(f"选择了 {len(frame_paths)} 帧进行检测")
        for i, path in enumerate(frame_paths):
            logger.info(f"  帧 {i}: {os.path.basename(path)}")
        
        # 验证序列
        if not data_processor.validate_sequence(frame_paths):
            logger.warning("序列验证失败，但继续处理")
        
        # 3. 初始化Qwen视频检测器
        logger.info("步骤3: 初始化Qwen2.5-VL模型")
        video_detector = QwenVideoDetector(args.model_path, logger=logger)
        
        # 打印模型信息
        model_info = video_detector.get_model_info()
        logger.info(f"模型信息: {model_info}")
        
        # 4. 准备视频输入
        logger.info("步骤4: 准备视频输入")
        video_input_paths = data_processor.prepare_video_input_paths(frame_paths)
        
        # 5. 进行视频序列检测
        logger.info("步骤5: 开始视频序列检测")
        start_time = time.time()
        
        detection_output = video_detector.detect_video_sequence(
            video_input_paths, 
            DETECTION_PROMPT, 
            max_new_tokens=args.max_tokens
        )
        
        detection_time = time.time() - start_time
        logger.info(f"检测完成，耗时: {detection_time:.2f}秒")
        logger.info(f"模型原始输出长度: {len(detection_output)}")
        
        # 保存原始输出
        raw_output_path = os.path.join(output_dirs['detections'], "raw_model_output.txt")
        with open(raw_output_path, 'w', encoding='utf-8') as f:
            f.write(f"检测时间: {datetime.now()}\n")
            f.write(f"检测耗时: {detection_time:.2f}秒\n")
            f.write(f"帧数: {len(frame_paths)}\n")
            f.write("=" * 50 + "\n")
            f.write(detection_output)
        logger.info(f"原始输出已保存: {raw_output_path}")
        
        # 6. 解析检测结果
        logger.info("步骤6: 解析检测结果")
        target_detector = TargetDetector(logger=logger)
        
        parsed_detections = target_detector.parse_detection_output(
            detection_output, frame_paths
        )
        
        # 验证检测结果
        validated_detections = target_detector.validate_detections(
            parsed_detections, frame_paths
        )
        
        logger.info(f"解析得到 {len(validated_detections)} 帧的检测结果")
        for frame_name, detections in validated_detections.items():
            logger.info(f"  {frame_name}: {len(detections)} 个目标")
        
        # 7. 保存检测结果
        logger.info("步骤7: 保存检测结果")
        detection_results = {
            'metadata': {
                'sequence_path': args.sequence_path,
                'total_frames': len(frame_paths),
                'detection_time': detection_time,
                'timestamp': datetime.now().isoformat()
            },
            'detections': validated_detections
        }
        
        results_path = os.path.join(output_dirs['detections'], "detection_results.json")
        save_detection_results(detection_results, results_path)
        
        # 8. 创建可视化
        logger.info("步骤8: 创建可视化结果")
        visualizer = DetectionVisualizer(output_dirs['visualizations'], logger)
        
        saved_visualizations = visualizer.visualize_detections(
            validated_detections,
            frame_paths,
            save_individual=args.save_individual,
            save_summary=args.save_summary
        )
        
        logger.info(f"保存了 {len(saved_visualizations)} 个可视化文件")
        
        # 9. 生成统计报告
        logger.info("步骤9: 生成统计报告")
        stats = visualizer.create_detection_statistics(validated_detections)
        stats_path = visualizer.save_statistics_report(stats)
        
        # 10. 清理资源
        logger.info("步骤10: 清理资源")
        video_detector.cleanup()
        
        # 输出最终结果
        logger.info("=" * 60)
        logger.info("检测完成！")
        logger.info("=" * 60)
        logger.info(f"序列路径: {args.sequence_path}")
        logger.info(f"处理帧数: {len(frame_paths)}")
        logger.info(f"检测耗时: {detection_time:.2f}秒")
        logger.info(f"检测到目标的帧数: {stats['frames_with_detections']}")
        logger.info(f"总检测数: {stats['total_detections']}")
        logger.info(f"输出目录: {args.output_dir}")
        logger.info("主要输出文件:")
        logger.info(f"  - 检测结果: {results_path}")
        logger.info(f"  - 原始输出: {raw_output_path}")
        logger.info(f"  - 统计报告: {stats_path}")
        logger.info(f"  - 可视化文件: {len(saved_visualizations)} 个")
        
        print("\n" + "=" * 60)
        print("检测完成！主要结果:")
        print(f"  - 处理了 {len(frame_paths)} 帧图像")
        print(f"  - 检测到目标的帧数: {stats['frames_with_detections']}")
        print(f"  - 总检测数: {stats['total_detections']}")
        print(f"  - 检测耗时: {detection_time:.2f}秒")
        print(f"  - 输出目录: {args.output_dir}")
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
