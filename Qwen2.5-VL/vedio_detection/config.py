"""
配置文件
"""
import os

# 模型配置
MODEL_PATH = "../Qwen2.5-VL-7B-Instruct"
DEVICE = "cuda:0"  # 由于设置了CUDA_VISIBLE_DEVICES=2，这里使用cuda:0即为物理2号GPU
TORCH_DTYPE = "auto"

# 数据配置
DATASET_PATH = "../tiaozhanbei_datasets"
CLASS_JSON_PATH = "../tiaozhanbei_datasets/class.json"

# 序列处理配置
FRAME_INTERVAL = 5  # 每隔5帧选取一帧
FRAMES_PER_GROUP = 20  # 每组20帧
SEQUENCE_NAME = "data04"

# 检测配置
DETECTION_PROMPT = """
请检测这个红外图像序列中的微小目标。这些图像是按时间顺序排列的连续帧，请利用目标在不同帧中的运动特征和空间特征进行检测。

请检测以下6类目标：
- drone (无人机)
- car (汽车) 
- ship (船只)
- bus (公交车)
- pedestrian (行人)
- cyclist (骑行者)

请以JSON格式输出每一帧中检测到的目标位置（绝对坐标）和类别信息，格式如下：
[
    {
        "frame": "frame_name",
        "detections": [
            {
                "bbox": [x1, y1, x2, y2],
                "class_id": 0,
                "class_name": "drone",
                "confidence": 0.95
            }
        ]
    }
]
"""

# 输出配置
OUTPUT_DIR = "./output"
DETECTION_OUTPUT_DIR = os.path.join(OUTPUT_DIR, "detections")
VISUALIZATION_OUTPUT_DIR = os.path.join(OUTPUT_DIR, "visualizations")
LOG_OUTPUT_DIR = os.path.join(OUTPUT_DIR, "logs")

# 可视化配置
BBOX_COLORS = {
    0: (255, 0, 0),      # drone - 红色
    1: (0, 255, 0),      # car - 绿色
    2: (0, 0, 255),      # ship - 蓝色
    3: (255, 255, 0),    # bus - 黄色
    4: (255, 0, 255),    # pedestrian - 紫色
    5: (0, 255, 255),    # cyclist - 青色
}

BBOX_THICKNESS = 2
FONT_SCALE = 0.6
FONT_THICKNESS = 2

# 类别映射
CLASS_NAMES = {
    0: "drone",
    1: "car", 
    2: "ship",
    3: "bus",
    4: "pedestrian",
    5: "cyclist"
}

# 视频处理配置
MIN_PIXELS = 256 * 28 * 28
MAX_PIXELS = 1280 * 28 * 28
FPS = 2.0

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
