#!/usr/bin/env python3
"""
检测结果转换脚本
将绝对坐标的检测结果转换为归一化的YOLO格式标签文件
支持8个序列的前20%帧
"""

import os
import json
import math
import re
from pathlib import Path
from typing import Dict, List, Tuple

def load_class_mapping(class_file: str) -> Dict[str, int]:
    """加载类别映射"""
    with open(class_file, 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    # 将类别名称映射到ID
    name_to_id = {v: int(k) for k, v in class_map.items()}
    return name_to_id

def get_image_size(sequence_name: str) -> Tuple[int, int]:
    """获取图像尺寸"""
    size_map = {
        'data01': (256, 256), 'data02': (256, 256), 'data04': (256, 256),
        'data05': (256, 256), 'data06': (256, 256), 'data07': (256, 256),
        'data23': (640, 512), 'data25': (640, 512), 'data26': (640, 512)
    }
    return size_map.get(sequence_name, (256, 256))

def convert_bbox_to_yolo(bbox: List[int], img_width: int, img_height: int) -> Tuple[float, float, float, float]:
    """
    将绝对坐标边界框转换为YOLO格式的归一化坐标
    
    Args:
        bbox: [x1, y1, x2, y2] 绝对坐标
        img_width: 图像宽度
        img_height: 图像高度
    
    Returns:
        (x_center, y_center, width, height) 归一化坐标
    """
    x1, y1, x2, y2 = bbox
    
    # 计算中心点和宽高
    x_center = (x1 + x2) / 2.0
    y_center = (y1 + y2) / 2.0
    width = x2 - x1
    height = y2 - y1
    
    # 归一化
    x_center_norm = x_center / img_width
    y_center_norm = y_center / img_height
    width_norm = width / img_width
    height_norm = height / img_height
    
    return x_center_norm, y_center_norm, width_norm, height_norm

def extract_frame_number(frame_path: str) -> int:
    """从帧路径中提取帧号"""
    # 提取文件名（去掉路径和扩展名）
    filename = os.path.basename(frame_path)
    name_without_ext = os.path.splitext(filename)[0]
    
    # 尝试直接转换为数字
    if name_without_ext.isdigit():
        return int(name_without_ext)
    
    # 如果不是纯数字，尝试提取最后的数字部分
    numbers = re.findall(r'\d+', name_without_ext)
    if numbers:
        return int(numbers[-1])  # 取最后一个数字
    
    # 如果都失败了，返回0
    print(f"警告: 无法从 '{frame_path}' 提取帧号，使用0")
    return 0

def get_frame_count_for_sequence(sequence_name: str, labels_root: str) -> int:
    """获取序列的总帧数"""
    labels_dir = os.path.join(labels_root, sequence_name)
    if not os.path.exists(labels_dir):
        return 0
    
    txt_files = [f for f in os.listdir(labels_dir) if f.endswith('.txt')]
    return len(txt_files)

def get_frames_to_process(sequence_name: str, labels_root: str, percentage: float = 0.2) -> int:
    """计算需要处理的帧数（前20%）"""
    total_frames = get_frame_count_for_sequence(sequence_name, labels_root)
    frames_to_process = math.ceil(total_frames * percentage)
    print(f"序列 {sequence_name}: 总帧数 {total_frames}, 处理前 {frames_to_process} 帧 ({percentage*100:.0f}%)")
    return frames_to_process

def convert_sequence_results(sequence_name: str, input_dir: str, output_dir: str, 
                           class_mapping: Dict[str, int], labels_root: str) -> bool:
    """
    转换单个序列的检测结果
    
    Args:
        sequence_name: 序列名称 (如 'data01')
        input_dir: 输入目录 (batch_output)
        output_dir: 输出目录
        class_mapping: 类别名称到ID的映射
        labels_root: 标签根目录，用于确定总帧数
    
    Returns:
        转换是否成功
    """
    sequence_input_dir = os.path.join(input_dir, sequence_name)
    sequence_output_dir = os.path.join(output_dir, sequence_name)
    
    # 检查输入目录
    if not os.path.exists(sequence_input_dir):
        print(f"警告: 输入目录不存在: {sequence_input_dir}")
        return False
    
    # 创建输出目录
    os.makedirs(sequence_output_dir, exist_ok=True)
    
    # 加载检测结果
    all_results_file = os.path.join(sequence_input_dir, 'all_detection_results.json')
    if not os.path.exists(all_results_file):
        print(f"警告: 检测结果文件不存在: {all_results_file}")
        return False
    
    with open(all_results_file, 'r', encoding='utf-8') as f:
        all_results = json.load(f)
    
    # 获取图像尺寸
    img_width, img_height = get_image_size(sequence_name)
    
    # 计算需要处理的帧数（前20%）
    frames_to_process = get_frames_to_process(sequence_name, labels_root)
    
    # 合并所有检测结果，同时提取帧号
    all_detections = {}
    frame_number_mapping = {}  # 帧路径到帧号的映射
    
    for result in all_results:
        for frame_path, detections in result.get('detections', {}).items():
            frame_number = extract_frame_number(frame_path)
            all_detections[frame_path] = detections
            frame_number_mapping[frame_path] = frame_number
    
    # 按帧号排序
    frame_paths = sorted(all_detections.keys(), key=lambda x: frame_number_mapping[x])
    
    # 只处理前20%的帧
    frames_to_convert = frame_paths[:frames_to_process]
    
    print(f"转换序列 {sequence_name}: 处理 {len(frames_to_convert)} / {len(frame_paths)} 帧")
    
    converted_count = 0
    for frame_path in frames_to_convert:
        detections = all_detections[frame_path]
        frame_number = frame_number_mapping[frame_path]
        
        # 创建YOLO格式的标签文件
        output_file = os.path.join(sequence_output_dir, f"{frame_number}.txt")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for detection in detections:
                bbox = detection['bbox']  # [x1, y1, x2, y2]
                class_name = detection['class_name']
                confidence = detection.get('confidence', 1.0)
                
                # 检查类别是否存在
                if class_name not in class_mapping:
                    print(f"警告: 未知类别 '{class_name}' 在帧 {frame_number}")
                    continue
                
                class_id = class_mapping[class_name]
                
                # 转换坐标
                x_center, y_center, width, height = convert_bbox_to_yolo(
                    bbox, img_width, img_height
                )
                
                # 写入YOLO格式
                f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
        
        converted_count += 1
    
    print(f"序列 {sequence_name} 转换完成: {converted_count} 个文件")
    return True

def main():
    """主函数"""
    # 配置
    config = {
        'input_dir': 'batch_output',  # 检测结果目录
        'output_dir': 'batch_output_yolo',  # 输出YOLO格式目录
        'class_mapping_file': '../tiaozhanbei_datasets/class.json',  # 类别映射文件
        'labels_root': '../tiaozhanbei_datasets/labels',  # 标签根目录，用于确定总帧数
        'sequences': ['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']  # 8个序列
    }
    
    print("开始转换检测结果为YOLO格式...")
    print(f"输入目录: {config['input_dir']}")
    print(f"输出目录: {config['output_dir']}")
    print(f"处理序列: {config['sequences']}")
    print(f"处理比例: 前20%帧")
    print("-" * 60)
    
    # 加载类别映射
    try:
        class_mapping = load_class_mapping(config['class_mapping_file'])
        print(f"加载类别映射: {class_mapping}")
    except Exception as e:
        print(f"错误: 无法加载类别映射文件 {config['class_mapping_file']}: {e}")
        return
    
    # 创建输出根目录
    os.makedirs(config['output_dir'], exist_ok=True)
    
    # 转换每个序列
    success_count = 0
    for sequence_name in config['sequences']:
        print(f"\n处理序列: {sequence_name}")
        success = convert_sequence_results(
            sequence_name, 
            config['input_dir'], 
            config['output_dir'], 
            class_mapping,
            config['labels_root']
        )
        if success:
            success_count += 1
    
    print(f"\n转换完成!")
    print(f"成功转换: {success_count}/{len(config['sequences'])} 个序列")
    print(f"输出目录: {config['output_dir']}")

if __name__ == "__main__":
    main()