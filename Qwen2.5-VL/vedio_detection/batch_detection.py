#!/usr/bin/env python3
"""
红外图像微小目标检测系统 - 主程序
支持单序列和批量处理，自适应不同数据格式
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from detection import InfraredTargetDetector
from visualization import DetectionVisualizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_detection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_sequence_info():
    """获取各序列的基本信息"""
    return {
        'data01': {'frames': 1500, 'size': '256x256', 'format': 'bmp'},
        'data02': {'frames': 763, 'size': '256x256', 'format': 'bmp'},
        'data04': {'frames': 399, 'size': '256x256', 'format': 'bmp'},
        'data05': {'frames': 399, 'size': '256x256', 'format': 'bmp'},
        'data06': {'frames': 399, 'size': '256x256', 'format': 'bmp'},
        'data07': {'frames': 399, 'size': '256x256', 'format': 'bmp'},
        'data23': {'frames': 850, 'size': '640x512', 'format': 'jpg'},
        'data25': {'frames': 1500, 'size': '640x512', 'format': 'jpg'},
        'data26': {'frames': 1500, 'size': '640x512', 'format': 'jpg'},
    }

def process_sequence(detector, visualizer, data_dir, sequence_name, output_base_dir, 
                    frame_percentage=0.2, frame_interval=5, sequence_length=5, max_sequences=None):
    """处理单个序列"""
    try:
        logger.info("=" * 60)
        logger.info(f"开始处理序列: {sequence_name}")
        logger.info("=" * 60)
        
        # 创建序列专用的输出目录
        sequence_output_dir = Path(output_base_dir) / sequence_name
        os.makedirs(sequence_output_dir, exist_ok=True)
        
        # 获取帧序列
        logger.info("获取帧序列...")
        sequences = detector.get_frame_sequence(
            data_dir, 
            sequence_name,
            frame_interval=frame_interval,
            sequence_length=sequence_length,
            frame_percentage=frame_percentage
        )
        
        # 限制处理序列数量（用于测试）
        if max_sequences:
            sequences = sequences[:max_sequences]
            logger.info(f"限制处理序列数量为: {max_sequences}")
        
        logger.info(f"将处理 {len(sequences)} 个帧序列")
        
        # 处理每个序列
        all_results = []
        for i, frame_paths in enumerate(sequences):
            logger.info(f"处理序列 {i+1}/{len(sequences)}")
            logger.info(f"帧文件: {[Path(p).name for p in frame_paths]}")
            
            try:
                # 检测
                result = detector.detect_sequence(frame_paths, sequence_name, i)
                
                # 保存结果到序列专用目录
                result_file = sequence_output_dir / f"detection_sequence_{i:03d}.json"
                with open(result_file, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(result, f, ensure_ascii=False, indent=2)
                
                all_results.append(result)
                logger.info(f"序列 {i+1} 检测完成，结果已保存到 {result_file}")
                
            except Exception as e:
                logger.error(f"序列 {i+1} 检测失败: {e}")
                continue
        
        # 保存汇总结果
        if all_results:
            summary_file = sequence_output_dir / "all_detection_results.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(all_results, f, ensure_ascii=False, indent=2)
            logger.info(f"汇总结果已保存到 {summary_file}")
        
        logger.info(f"序列 {sequence_name} 检测完成: {len(all_results)}/{len(sequences)} 个序列成功")
        
        # 可视化
        logger.info("开始结果可视化...")
        try:
            visualizer.visualize_all_results(str(sequence_output_dir), str(sequence_output_dir))
            logger.info("可视化完成！")
        except Exception as e:
            logger.error(f"可视化失败: {e}")
        
        return len(all_results), len(sequences)
        
    except Exception as e:
        logger.error(f"处理序列 {sequence_name} 失败: {e}")
        return 0, 0

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='红外图像微小目标检测系统',
        epilog="""
使用示例:
  # 处理单个序列
  python batch_detection.py --sequences data04 --frame_percentage 0.2

  # 批量处理多个序列
  python batch_detection.py --sequences data01 data04 data23 --frame_percentage 0.2

  # 处理所有序列
  python batch_detection.py --frame_percentage 0.2

  # 测试模式（限制序列数量）
  python batch_detection.py --sequences data04 --max_sequences 3
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--data_dir', 
        type=str, 
        default='../tiaozhanbei_datasets',
        help='数据集根目录'
    )
    
    parser.add_argument(
        '--sequences', 
        type=str, 
        nargs='+',
        default=['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26'],
        help='要处理的序列列表'
    )
    
    parser.add_argument(
        '--output_dir', 
        type=str, 
        default='./batch_output',
        help='输出目录'
    )
    
    parser.add_argument(
        '--frame_percentage', 
        type=float, 
        default=0.2,
        help='处理帧的百分比（0.0-1.0）'
    )
    
    parser.add_argument(
        '--frame_interval', 
        type=int, 
        default=5,
        help='帧间隔'
    )
    
    parser.add_argument(
        '--sequence_length', 
        type=int, 
        default=5,
        help='每个序列的帧数'
    )
    
    parser.add_argument(
        '--max_sequences', 
        type=int, 
        default=None,
        help='每个序列最大处理数量（用于测试）'
    )
    
    parser.add_argument(
        '--model_path', 
        type=str, 
        default='../Qwen2.5-VL-7B-Instruct',
        help='模型路径'
    )
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取序列信息
    sequence_info = get_sequence_info()
    
    logger.info("=" * 80)
    logger.info("批量红外图像微小目标检测系统启动")
    logger.info("=" * 80)
    logger.info(f"数据目录: {args.data_dir}")
    logger.info(f"输出目录: {args.output_dir}")
    logger.info(f"处理序列: {args.sequences}")
    logger.info(f"帧百分比: {args.frame_percentage*100:.1f}%")
    logger.info(f"帧间隔: {args.frame_interval}")
    logger.info(f"序列长度: {args.sequence_length}")
    if args.max_sequences:
        logger.info(f"最大序列数: {args.max_sequences}")
    
    try:
        # 初始化检测器和可视化器
        logger.info("初始化检测器...")
        detector = InfraredTargetDetector(model_path=args.model_path)
        
        logger.info("初始化可视化器...")
        visualizer = DetectionVisualizer()
        
        # 处理每个序列
        total_success = 0
        total_sequences = 0
        
        for sequence_name in args.sequences:
            if sequence_name not in sequence_info:
                logger.warning(f"未知序列: {sequence_name}，跳过")
                continue
            
            info = sequence_info[sequence_name]
            logger.info(f"序列信息 - {sequence_name}: {info['frames']}帧, {info['size']}, {info['format'].upper()}格式")
            
            success, total = process_sequence(
                detector, visualizer, args.data_dir, sequence_name, args.output_dir,
                args.frame_percentage, args.frame_interval, args.sequence_length, args.max_sequences
            )
            
            total_success += success
            total_sequences += total
        
        logger.info("=" * 80)
        logger.info("批量处理完成！")
        logger.info(f"总体成功率: {total_success}/{total_sequences} ({total_success/total_sequences*100:.1f}%)")
        logger.info(f"结果保存在: {args.output_dir}")
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
